"""
Cerberus 边缘节点管理器
负责节点注册、状态报告和配置管理
"""

import time
import logging
import psutil
import platform
import requests
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class NodeManager:
    """节点管理器"""
    
    def __init__(self, config):
        self.config = config
        self.node_id = config.NODE_ID
        self.central_platform_url = config.CENTRAL_PLATFORM_URL
        self.start_time = time.time()
        self.last_heartbeat = 0
        self.registration_status = False
        
        logger.info(f"节点管理器初始化完成: {self.node_id}")
    
    def register_node(self) -> bool:
        """注册节点到中央平台"""
        try:
            node_info = self._get_node_info()
            
            response = requests.post(
                f"{self.central_platform_url}/api/v1/nodes",
                json=node_info,
                timeout=10
            )
            
            if response.status_code in [200, 201, 409]:  # 409表示已存在
                self.registration_status = True
                logger.info(f"节点注册成功: {self.node_id}")
                return True
            else:
                logger.error(f"节点注册失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"节点注册异常: {e}")
            return False
    
    def _get_node_info(self) -> Dict[str, Any]:
        """获取节点信息"""
        try:
            # 系统信息
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'architecture': platform.architecture()[0],
                'python_version': platform.python_version()
            }
            
            # 硬件信息
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            hardware_info = {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'disk_total_gb': round(disk.total / (1024**3), 2),
                'disk_free_gb': round(disk.free / (1024**3), 2)
            }
            
            # GPU信息
            gpu_info = self._get_gpu_info()
            
            return {
                'node_id': self.node_id,
                'name': f"Edge Node {self.node_id}",
                'description': f"Cerberus边缘处理节点 - {self.node_id}",
                'location': 'Unknown',
                'ip_address': self._get_local_ip(),
                'version': '2.0.0',
                'config': {
                    'video_resolution': self.config.VIDEO_RESOLUTION,
                    'video_fps': self.config.VIDEO_FPS,
                    'detection_interval': self.config.DETECTION_INTERVAL,
                    'pre_record_seconds': self.config.PRE_RECORD_SECONDS,
                    'post_record_seconds': self.config.POST_RECORD_SECONDS
                },
                'metadata': {
                    'system': system_info,
                    'hardware': hardware_info,
                    'gpu': gpu_info,
                    'start_time': self.start_time
                }
            }
            
        except Exception as e:
            logger.error(f"获取节点信息失败: {e}")
            return {
                'node_id': self.node_id,
                'name': f"Edge Node {self.node_id}",
                'description': f"Cerberus边缘处理节点 - {self.node_id}",
                'version': '2.0.0'
            }
    
    def _get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""
        try:
            import pynvml
            pynvml.nvmlInit()
            
            gpu_count = pynvml.nvmlDeviceGetCount()
            gpus = []
            
            for i in range(gpu_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                gpus.append({
                    'index': i,
                    'name': name,
                    'memory_total_mb': memory_info.total // (1024**2),
                    'memory_free_mb': memory_info.free // (1024**2),
                    'memory_used_mb': memory_info.used // (1024**2)
                })
            
            return {
                'available': True,
                'count': gpu_count,
                'devices': gpus
            }
            
        except Exception:
            return {
                'available': False,
                'count': 0,
                'devices': []
            }
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # GPU使用率
            gpu_usage = 0
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                gpu_util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_usage = gpu_util.gpu
            except:
                pass
            
            # 网络状态
            network_status = self._check_network_connectivity()
            
            # 确定整体健康状态
            status = "healthy"
            if cpu_percent > 90 or memory_percent > 90 or disk_percent > 90:
                status = "warning"
            if not network_status:
                status = "error"
            
            return {
                'status': status,
                'timestamp': time.time(),
                'uptime': time.time() - self.start_time,
                'cpu_usage': cpu_percent,
                'memory_usage': memory_percent,
                'disk_usage': disk_percent,
                'gpu_usage': gpu_usage,
                'network_connected': network_status,
                'registration_status': self.registration_status,
                'last_heartbeat': self.last_heartbeat
            }
            
        except Exception as e:
            logger.error(f"获取健康状态失败: {e}")
            return {
                'status': 'error',
                'timestamp': time.time(),
                'error': str(e)
            }
    
    def _check_network_connectivity(self) -> bool:
        """检查网络连接"""
        try:
            response = requests.get(
                f"{self.central_platform_url}/health",
                timeout=5
            )
            return response.status_code == 200
        except:
            return False
    
    def send_heartbeat(self, additional_data: Optional[Dict[str, Any]] = None) -> bool:
        """发送心跳"""
        try:
            heartbeat_data = {
                'node_id': self.node_id,
                'timestamp': time.time(),
                'status': self.get_health_status()
            }
            
            if additional_data:
                heartbeat_data.update(additional_data)
            
            response = requests.post(
                f"{self.central_platform_url}/api/v1/nodes/{self.node_id}/heartbeat",
                json=heartbeat_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.last_heartbeat = time.time()
                return True
            else:
                logger.warning(f"心跳发送失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送心跳异常: {e}")
            return False
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            # 这里应该实现配置更新逻辑
            # 可以更新内存中的配置，并保存到文件
            logger.info(f"收到配置更新: {new_config}")
            
            # 发送确认
            response = requests.post(
                f"{self.central_platform_url}/api/v1/nodes/{self.node_id}/config/ack",
                json={'status': 'success', 'timestamp': time.time()},
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def get_uptime(self) -> float:
        """获取运行时间"""
        return time.time() - self.start_time
    
    def get_uptime_formatted(self) -> str:
        """获取格式化的运行时间"""
        uptime_seconds = self.get_uptime()
        
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分钟"
        elif hours > 0:
            return f"{hours}小时 {minutes}分钟"
        else:
            return f"{minutes}分钟 {seconds}秒"
