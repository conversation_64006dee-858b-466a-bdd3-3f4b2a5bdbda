#!/usr/bin/env python3
"""
Cerberus Edge Node - 边缘处理节点主程序
负责实时视频分析、事件检测和数据上传
"""

import os
import sys
import time
import signal
import logging
import threading
from typing import Dict, List
from datetime import datetime

from flask import Flask, jsonify, request
from dotenv import load_dotenv

from app.core.config import Config
from app.core.node_manager import NodeManager
from app.video.stream_manager import StreamManager
from app.ai.detection_engine import DetectionEngine
from app.mqtt.publisher import MQTTPublisher
from app.storage.local_cache import LocalCache
from app.storage.minio_client import MinIOClient

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/edge_node.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class EdgeNode:
    """边缘处理节点主类"""
    
    def __init__(self):
        self.config = Config()
        self.running = False
        self.threads = []
        
        # 初始化组件
        self.node_manager = NodeManager(self.config)
        self.stream_manager = StreamManager(self.config)
        self.detection_engine = DetectionEngine(self.config)
        self.mqtt_publisher = MQTTPublisher(self.config)
        self.local_cache = LocalCache(self.config)
        self.minio_client = MinIOClient(self.config)
        
        # Flask应用用于健康检查和远程管理
        self.app = Flask(__name__)
        self.setup_routes()
        
    def setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/health')
        def health_check():
            """健康检查接口"""
            status = self.node_manager.get_health_status()
            return jsonify(status)
        
        @self.app.route('/status')
        def get_status():
            """获取节点详细状态"""
            status = {
                'node_id': self.config.NODE_ID,
                'uptime': self.node_manager.get_uptime(),
                'cameras': self.stream_manager.get_camera_status(),
                'detection_stats': self.detection_engine.get_stats(),
                'cache_info': self.local_cache.get_info(),
                'mqtt_connected': self.mqtt_publisher.is_connected()
            }
            return jsonify(status)
        
        @self.app.route('/cameras', methods=['GET'])
        def list_cameras():
            """获取摄像头列表"""
            cameras = self.stream_manager.get_cameras()
            return jsonify(cameras)
        
        @self.app.route('/cameras/<camera_id>/start', methods=['POST'])
        def start_camera(camera_id):
            """启动指定摄像头"""
            try:
                self.stream_manager.start_camera(camera_id)
                return jsonify({'status': 'success', 'message': f'Camera {camera_id} started'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/cameras/<camera_id>/stop', methods=['POST'])
        def stop_camera(camera_id):
            """停止指定摄像头"""
            try:
                self.stream_manager.stop_camera(camera_id)
                return jsonify({'status': 'success', 'message': f'Camera {camera_id} stopped'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/config', methods=['GET'])
        def get_config():
            """获取当前配置"""
            return jsonify(self.config.to_dict())
        
        @self.app.route('/config', methods=['POST'])
        def update_config():
            """更新配置"""
            try:
                new_config = request.get_json()
                self.config.update(new_config)
                return jsonify({'status': 'success', 'message': 'Configuration updated'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)}), 500
    
    def start(self):
        """启动边缘节点"""
        logger.info(f"Starting Cerberus Edge Node: {self.config.NODE_ID}")
        
        try:
            # 初始化所有组件
            self.initialize_components()
            
            # 启动后台线程
            self.start_background_threads()
            
            # 标记为运行状态
            self.running = True
            
            # 启动Flask应用
            self.app.run(
                host=self.config.FLASK_HOST,
                port=self.config.FLASK_PORT,
                debug=self.config.DEBUG,
                threaded=True
            )
            
        except Exception as e:
            logger.error(f"Failed to start edge node: {e}")
            self.stop()
            raise
    
    def initialize_components(self):
        """初始化所有组件"""
        logger.info("Initializing components...")
        
        # 初始化本地缓存
        self.local_cache.initialize()
        
        # 初始化MQTT连接
        self.mqtt_publisher.connect()
        
        # 初始化MinIO客户端
        self.minio_client.initialize()
        
        # 加载AI模型
        self.detection_engine.load_models()
        
        # 注册节点到中央平台
        self.node_manager.register_node()

        # 加载摄像头配置
        self.stream_manager.load_cameras_from_config()

        # 添加事件回调
        self.stream_manager.add_event_callback(self.handle_detection_event)

        logger.info("All components initialized successfully")

    def handle_detection_event(self, camera_id: str, detections: list, timestamp: float):
        """处理检测事件"""
        try:
            for detection in detections:
                # 创建事件数据
                event_data = {
                    'event_id': f"{camera_id}_{int(timestamp * 1000)}_{detection.detection_type}",
                    'node_id': self.config.NODE_ID,
                    'camera_id': camera_id,
                    'event_type': detection.detection_type,
                    'confidence': detection.confidence,
                    'bbox': detection.bbox,
                    'timestamp': timestamp,
                    'class_name': getattr(detection, 'class_name', None),
                    'metadata': detection.metadata
                }

                # 缓存事件
                self.local_cache.cache_event(event_data)

                # 发布到MQTT
                if self.mqtt_publisher.is_connected():
                    self.mqtt_publisher.publish_event(event_data)

                logger.info(f"检测事件处理完成: {event_data['event_id']}")

        except Exception as e:
            logger.error(f"处理检测事件失败: {e}")

    def start_background_threads(self):
        """启动后台线程"""
        logger.info("Starting background threads...")

        # 心跳线程
        heartbeat_thread = threading.Thread(target=self.heartbeat_loop, daemon=True)
        heartbeat_thread.start()
        self.threads.append(heartbeat_thread)

        # 数据同步线程
        sync_thread = threading.Thread(target=self.sync_loop, daemon=True)
        sync_thread.start()
        self.threads.append(sync_thread)

        # 视频上传线程
        upload_thread = threading.Thread(target=self.upload_loop, daemon=True)
        upload_thread.start()
        self.threads.append(upload_thread)

        logger.info(f"Started {len(self.threads)} background threads")

    def heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                # 获取系统状态
                system_stats = self.stream_manager.get_system_stats()

                # 发送心跳到中央平台
                self.node_manager.send_heartbeat(system_stats)

                # 发布状态到MQTT
                if self.mqtt_publisher.is_connected():
                    health_status = self.node_manager.get_health_status()
                    self.mqtt_publisher.publish_status(health_status)

                time.sleep(30)  # 30秒间隔

            except Exception as e:
                logger.error(f"心跳任务错误: {e}")
                time.sleep(60)

    def sync_loop(self):
        """数据同步循环"""
        while self.running:
            try:
                # 同步待处理事件
                synced_count = self.local_cache.sync_pending_events()
                if synced_count > 0:
                    logger.info(f"数据同步完成: {synced_count} 个事件")

                time.sleep(60)  # 1分钟间隔

            except Exception as e:
                logger.error(f"数据同步任务错误: {e}")
                time.sleep(300)

    def upload_loop(self):
        """视频上传循环"""
        while self.running:
            try:
                # 上传待上传的视频
                uploaded_count = self.minio_client.upload_pending_videos()
                if uploaded_count > 0:
                    logger.info(f"视频上传完成: {uploaded_count} 个文件")

                time.sleep(300)  # 5分钟间隔

            except Exception as e:
                logger.error(f"视频上传任务错误: {e}")
                time.sleep(600)

    def stop(self):
        """停止边缘节点"""
        logger.info("Stopping Cerberus Edge Node...")

        self.running = False

        # 停止流管理器
        self.stream_manager.cleanup()

        # 断开MQTT连接
        self.mqtt_publisher.disconnect()

        # 关闭本地缓存
        self.local_cache.close()

        # 清理MinIO客户端
        self.minio_client.cleanup()

        # 等待后台线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)

        logger.info("Edge node stopped successfully")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，开始优雅关闭...")
    if 'edge_node' in globals():
        edge_node.stop()
    sys.exit(0)

if __name__ == '__main__':
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建必要目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('cache', exist_ok=True)
    os.makedirs('cache/videos', exist_ok=True)
    os.makedirs('cache/events', exist_ok=True)
    os.makedirs('models', exist_ok=True)

    try:
        # 创建并启动边缘节点
        edge_node = EdgeNode()
        edge_node.start()

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(f"边缘节点启动失败: {e}")
        sys.exit(1)
    finally:
        if 'edge_node' in locals():
            edge_node.stop()
    
    def start_background_threads(self):
        """启动后台线程"""
        logger.info("Starting background threads...")
        
        # 状态报告线程
        status_thread = threading.Thread(
            target=self.status_reporter_loop,
            name="StatusReporter",
            daemon=True
        )
        status_thread.start()
        self.threads.append(status_thread)
        
        # 视频处理线程
        video_thread = threading.Thread(
            target=self.video_processing_loop,
            name="VideoProcessor",
            daemon=True
        )
        video_thread.start()
        self.threads.append(video_thread)
        
        # 数据同步线程
        sync_thread = threading.Thread(
            target=self.data_sync_loop,
            name="DataSync",
            daemon=True
        )
        sync_thread.start()
        self.threads.append(sync_thread)
        
        logger.info(f"Started {len(self.threads)} background threads")
    
    def status_reporter_loop(self):
        """状态报告循环"""
        while self.running:
            try:
                status = self.node_manager.get_health_status()
                self.mqtt_publisher.publish_status(status)
                time.sleep(self.config.STATUS_REPORT_INTERVAL)
            except Exception as e:
                logger.error(f"Error in status reporter: {e}")
                time.sleep(5)
    
    def video_processing_loop(self):
        """视频处理循环"""
        while self.running:
            try:
                # 处理所有活跃的视频流
                for camera_id in self.stream_manager.get_active_cameras():
                    frame = self.stream_manager.get_latest_frame(camera_id)
                    if frame is not None:
                        # AI检测
                        detections = self.detection_engine.detect(camera_id, frame)
                        
                        # 处理检测结果
                        if detections:
                            self.handle_detections(camera_id, frame, detections)
                
                time.sleep(0.1)  # 100ms间隔
                
            except Exception as e:
                logger.error(f"Error in video processing: {e}")
                time.sleep(1)
    
    def data_sync_loop(self):
        """数据同步循环"""
        while self.running:
            try:
                # 同步缓存的事件数据
                self.local_cache.sync_pending_events()
                
                # 上传待上传的视频文件
                self.minio_client.upload_pending_videos()
                
                time.sleep(self.config.DATA_SYNC_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in data sync: {e}")
                time.sleep(10)
    
    def handle_detections(self, camera_id: str, frame, detections: List[Dict]):
        """处理检测结果"""
        for detection in detections:
            try:
                # 生成事件ID
                event_id = self.generate_event_id()
                
                # 录制事件视频
                video_path = self.stream_manager.record_event_video(
                    camera_id, event_id, detection
                )
                
                # 构建事件数据
                event_data = {
                    'event_id': event_id,
                    'node_id': self.config.NODE_ID,
                    'camera_id': camera_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'detection_type': detection['type'],
                    'confidence': detection['confidence'],
                    'bbox': detection.get('bbox'),
                    'video_path': video_path,
                    'metadata': detection.get('metadata', {})
                }
                
                # 发布事件到MQTT
                self.mqtt_publisher.publish_event(event_data)
                
                # 缓存事件数据
                self.local_cache.cache_event(event_data)
                
                logger.info(f"Detected {detection['type']} on camera {camera_id}")
                
            except Exception as e:
                logger.error(f"Error handling detection: {e}")
    
    def generate_event_id(self) -> str:
        """生成唯一事件ID"""
        timestamp = int(time.time() * 1000)
        return f"{self.config.NODE_ID}_{timestamp}"
    
    def stop(self):
        """停止边缘节点"""
        logger.info("Stopping Cerberus Edge Node...")
        
        self.running = False
        
        # 停止所有组件
        if hasattr(self, 'stream_manager'):
            self.stream_manager.stop_all()
        
        if hasattr(self, 'mqtt_publisher'):
            self.mqtt_publisher.disconnect()
        
        if hasattr(self, 'local_cache'):
            self.local_cache.close()
        
        logger.info("Edge node stopped")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down...")
    if 'edge_node' in globals():
        edge_node.stop()
    sys.exit(0)

if __name__ == '__main__':
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动边缘节点
    edge_node = EdgeNode()
    edge_node.start()
