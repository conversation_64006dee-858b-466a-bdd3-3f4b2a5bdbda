"""
MQTT订阅器
"""

import logging
import json
import asyncio
from typing import Dict, Any, Callable
import paho.mqtt.client as mqtt

from app.core.config import get_settings, MQTTTopics

logger = logging.getLogger(__name__)

class MQTTSubscriber:
    """MQTT订阅器"""
    
    def __init__(self, settings=None):
        self.settings = settings or get_settings()
        self.client = None
        self.is_running = False
        self.message_handlers: Dict[str, Callable] = {}
        
    async def start(self):
        """启动MQTT订阅器"""
        try:
            self.client = mqtt.Client()
            self.client.on_connect = self._on_connect
            self.client.on_message = self._on_message
            self.client.on_disconnect = self._on_disconnect
            
            # 设置认证信息
            if self.settings.MQTT_USERNAME and self.settings.MQTT_PASSWORD:
                self.client.username_pw_set(
                    self.settings.MQTT_USERNAME,
                    self.settings.MQTT_PASSWORD
                )
            
            # 连接到MQTT代理
            self.client.connect(
                self.settings.MQTT_BROKER_HOST,
                self.settings.MQTT_BROKER_PORT,
                self.settings.MQTT_KEEPALIVE
            )
            
            # 启动循环
            self.client.loop_start()
            self.is_running = True
            
            logger.info(f"MQTT subscriber started, connecting to {self.settings.MQTT_BROKER_HOST}:{self.settings.MQTT_BROKER_PORT}")
            
        except Exception as e:
            logger.error(f"Error starting MQTT subscriber: {e}")
            raise
    
    async def stop(self):
        """停止MQTT订阅器"""
        try:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()
                self.client = None
            
            self.is_running = False
            logger.info("MQTT subscriber stopped")
            
        except Exception as e:
            logger.error(f"Error stopping MQTT subscriber: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.client and self.client.is_connected() if self.client else False
    
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            logger.info("MQTT subscriber connected successfully")
            # 订阅主题
            self._subscribe_topics()
        else:
            logger.error(f"MQTT connection failed with code {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        if rc != 0:
            logger.warning("MQTT subscriber disconnected unexpectedly")
        else:
            logger.info("MQTT subscriber disconnected")
    
    def _on_message(self, client, userdata, msg):
        """消息回调"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            logger.debug(f"Received MQTT message on topic {topic}: {payload}")
            
            # 解析JSON消息
            try:
                message_data = json.loads(payload)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in MQTT message: {payload}")
                return
            
            # 处理消息
            asyncio.create_task(self._handle_message(topic, message_data))
            
        except Exception as e:
            logger.error(f"Error processing MQTT message: {e}")
    
    async def _handle_message(self, topic: str, data: Dict[str, Any]):
        """处理MQTT消息"""
        try:
            # 根据主题类型处理消息
            if topic.startswith("cerberus/nodes/") and topic.endswith("/status"):
                await self._handle_node_status(topic, data)
            elif topic.startswith("cerberus/events/"):
                await self._handle_event(topic, data)
            elif topic.startswith("cerberus/config/") and topic.endswith("/response"):
                await self._handle_config_response(topic, data)
            else:
                logger.debug(f"Unhandled MQTT topic: {topic}")
                
        except Exception as e:
            logger.error(f"Error handling MQTT message for topic {topic}: {e}")
    
    async def _handle_node_status(self, topic: str, data: Dict[str, Any]):
        """处理节点状态消息"""
        try:
            # 从主题中提取节点ID
            parts = topic.split('/')
            if len(parts) >= 3:
                node_id = parts[2]
                logger.info(f"Received status update for node {node_id}: {data}")
                
                # TODO: 更新数据库中的节点状态
                # 这里可以调用NodeService来更新节点状态
                
        except Exception as e:
            logger.error(f"Error handling node status: {e}")
    
    async def _handle_event(self, topic: str, data: Dict[str, Any]):
        """处理事件消息"""
        try:
            # 从主题中提取节点ID和摄像头ID
            parts = topic.split('/')
            if len(parts) >= 4:
                node_id = parts[2]
                camera_id = parts[3]
                logger.info(f"Received event from node {node_id}, camera {camera_id}: {data}")
                
                # TODO: 保存事件到数据库
                # 这里可以调用EventService来保存事件
                
        except Exception as e:
            logger.error(f"Error handling event: {e}")
    
    async def _handle_config_response(self, topic: str, data: Dict[str, Any]):
        """处理配置响应消息"""
        try:
            # 从主题中提取节点ID
            parts = topic.split('/')
            if len(parts) >= 3:
                node_id = parts[2]
                logger.info(f"Received config response from node {node_id}: {data}")
                
                # TODO: 处理配置响应
                
        except Exception as e:
            logger.error(f"Error handling config response: {e}")
    
    def _subscribe_topics(self):
        """订阅MQTT主题"""
        try:
            topics = [
                (MQTTTopics.NODE_STATUS_WILDCARD, self.settings.MQTT_QOS),
                (MQTTTopics.EVENT_WILDCARD, self.settings.MQTT_QOS),
                (MQTTTopics.SYSTEM_ALERT, self.settings.MQTT_QOS),
                (MQTTTopics.SYSTEM_HEARTBEAT, self.settings.MQTT_QOS),
            ]
            
            for topic, qos in topics:
                self.client.subscribe(topic, qos)
                logger.info(f"Subscribed to MQTT topic: {topic}")
                
        except Exception as e:
            logger.error(f"Error subscribing to MQTT topics: {e}")
    
    async def publish(self, topic: str, payload: Dict[str, Any], qos: int = None):
        """发布MQTT消息"""
        try:
            if not self.is_connected():
                logger.warning("MQTT client not connected, cannot publish message")
                return False
            
            qos = qos or self.settings.MQTT_QOS
            message = json.dumps(payload)
            
            result = self.client.publish(topic, message, qos)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.debug(f"Published MQTT message to {topic}: {message}")
                return True
            else:
                logger.error(f"Failed to publish MQTT message to {topic}")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing MQTT message: {e}")
            return False
