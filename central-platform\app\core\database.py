"""
数据库连接和管理
"""

import logging
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool

from app.core.config import get_settings
from app.models.database import Base

logger = logging.getLogger(__name__)

# 全局变量
engine = None
async_session_maker = None

def get_database_url() -> str:
    """获取数据库连接URL"""
    settings = get_settings()
    # 将同步URL转换为异步URL
    url = settings.DATABASE_URL
    if url.startswith("postgresql://"):
        url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
    return url

def create_engine():
    """创建数据库引擎"""
    global engine
    if engine is None:
        database_url = get_database_url()
        engine = create_async_engine(
            database_url,
            poolclass=NullPool,
            echo=False,
        )
    return engine

def create_session_maker():
    """创建会话工厂"""
    global async_session_maker
    if async_session_maker is None:
        engine = create_engine()
        async_session_maker = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    return async_session_maker

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    session_maker = create_session_maker()
    async with session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

async def create_tables():
    """创建数据库表"""
    try:
        engine = create_engine()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise

async def drop_tables():
    """删除数据库表"""
    try:
        engine = create_engine()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Error dropping database tables: {e}")
        raise

async def close_db():
    """关闭数据库连接"""
    global engine
    if engine:
        await engine.dispose()
        engine = None
        logger.info("Database connection closed")
