"""
增强人脸识别检测器
支持人脸检测、人脸识别、人脸数据库管理、陌生人检测、人脸质量评估等功能
"""

import cv2
import numpy as np
import time
import os
import pickle
import logging
import face_recognition
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from .detection_engine import BaseDetector, DetectionResult

logger = logging.getLogger(__name__)

@dataclass
class FaceInfo:
    """人脸信息"""
    person_id: str
    person_name: str
    encoding: np.ndarray
    confidence: float
    bbox: Tuple[int, int, int, int]
    landmarks: Optional[List[Tuple[int, int]]]
    quality_score: float
    is_frontal: bool

@dataclass
class PersonProfile:
    """人员档案"""
    person_id: str
    name: str
    encodings: List[np.ndarray]
    photos: List[str]
    created_time: float
    last_seen: Optional[float]
    total_detections: int
    metadata: Dict[str, Any]

class FaceRecognitionDetector(BaseDetector):
    """增强人脸识别检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 配置参数
        self.face_database_path = config.get('face_database', 'data/faces/')
        self.min_face_size = config.get('min_face_size', 50)
        self.max_face_size = config.get('max_face_size', 300)
        self.quality_threshold = config.get('quality_threshold', 0.6)
        self.recognition_threshold = config.get('recognition_threshold', 0.6)
        self.stranger_alert_enabled = config.get('stranger_alert_enabled', True)
        self.face_encoding_tolerance = config.get('face_encoding_tolerance', 0.6)
        
        # 人脸检测模型选择
        self.detection_method = config.get('detection_method', 'hog')  # 'hog' 或 'cnn'
        
        # 人脸数据库
        self.known_faces: Dict[str, PersonProfile] = {}
        self.face_encodings_list = []
        self.face_names_list = []
        
        # 统计信息
        self.detection_stats = {
            'total_faces_detected': 0,
            'known_faces_recognized': 0,
            'strangers_detected': 0,
            'low_quality_faces': 0
        }
        
        # 创建必要目录
        os.makedirs(self.face_database_path, exist_ok=True)
        os.makedirs(os.path.join(self.face_database_path, 'profiles'), exist_ok=True)
        os.makedirs(os.path.join(self.face_database_path, 'photos'), exist_ok=True)
        
        logger.info("增强人脸识别检测器初始化完成")
    
    def load_model(self) -> bool:
        """加载人脸识别模型和数据库"""
        try:
            # 加载已知人脸数据库
            self._load_face_database()
            logger.info(f"人脸识别模型加载成功，已加载 {len(self.known_faces)} 个已知人脸")
            return True
            
        except Exception as e:
            logger.error(f"人脸识别模型加载失败: {e}")
            return False
    
    def _load_face_database(self):
        """加载人脸数据库"""
        profiles_dir = os.path.join(self.face_database_path, 'profiles')
        
        if not os.path.exists(profiles_dir):
            return
        
        for filename in os.listdir(profiles_dir):
            if filename.endswith('.pkl'):
                profile_path = os.path.join(profiles_dir, filename)
                try:
                    with open(profile_path, 'rb') as f:
                        profile: PersonProfile = pickle.load(f)
                        self.known_faces[profile.person_id] = profile
                        
                        # 添加到快速查找列表
                        for encoding in profile.encodings:
                            self.face_encodings_list.append(encoding)
                            self.face_names_list.append(profile.person_id)
                
                except Exception as e:
                    logger.error(f"加载人脸档案失败 {filename}: {e}")
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行人脸检测和识别"""
        if not self.enabled:
            return []
        
        try:
            current_time = time.time()
            results = []
            
            # 缩放图像以提高性能
            small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)
            rgb_small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)
            
            # 检测人脸位置
            face_locations = face_recognition.face_locations(
                rgb_small_frame, 
                model=self.detection_method
            )
            
            if not face_locations:
                return results
            
            # 计算人脸编码
            face_encodings = face_recognition.face_encodings(
                rgb_small_frame, 
                face_locations
            )
            
            # 处理每个检测到的人脸
            for i, (face_encoding, face_location) in enumerate(zip(face_encodings, face_locations)):
                # 还原到原始图像坐标
                top, right, bottom, left = face_location
                top *= 4
                right *= 4
                bottom *= 4
                left *= 4
                
                bbox = (left, top, right, bottom)
                
                # 检查人脸大小
                face_width = right - left
                face_height = bottom - top
                
                if (face_width < self.min_face_size or face_height < self.min_face_size or
                    face_width > self.max_face_size or face_height > self.max_face_size):
                    continue
                
                # 评估人脸质量
                face_roi = frame[top:bottom, left:right]
                quality_score = self._evaluate_face_quality(face_roi)
                
                if quality_score < self.quality_threshold:
                    self.detection_stats['low_quality_faces'] += 1
                    continue
                
                # 人脸识别
                person_id, confidence = self._recognize_face(face_encoding)
                
                # 创建检测结果
                if person_id:
                    # 识别到已知人脸
                    person_profile = self.known_faces[person_id]
                    person_profile.last_seen = current_time
                    person_profile.total_detections += 1
                    
                    result = DetectionResult(
                        detection_type='face_recognition',
                        confidence=confidence,
                        bbox=bbox,
                        timestamp=current_time,
                        metadata={
                            'person_id': person_id,
                            'person_name': person_profile.name,
                            'quality_score': quality_score,
                            'recognition_confidence': confidence,
                            'is_known': True,
                            'total_detections': person_profile.total_detections,
                            'last_seen': person_profile.last_seen
                        }
                    )
                    
                    self.detection_stats['known_faces_recognized'] += 1
                    
                else:
                    # 陌生人检测
                    if self.stranger_alert_enabled:
                        result = DetectionResult(
                            detection_type='stranger_detection',
                            confidence=0.8,
                            bbox=bbox,
                            timestamp=current_time,
                            metadata={
                                'person_id': 'unknown',
                                'person_name': 'Unknown Person',
                                'quality_score': quality_score,
                                'is_known': False,
                                'face_encoding': face_encoding.tolist()  # 保存编码用于后续分析
                            }
                        )
                        
                        self.detection_stats['strangers_detected'] += 1
                    else:
                        continue
                
                results.append(result)
                self.detection_stats['total_faces_detected'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"人脸检测失败: {e}")
            return []
    
    def _evaluate_face_quality(self, face_roi: np.ndarray) -> float:
        """评估人脸质量"""
        if face_roi.size == 0:
            return 0.0
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            
            # 计算清晰度（拉普拉斯方差）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_score = min(laplacian_var / 1000.0, 1.0)
            
            # 计算亮度
            brightness = np.mean(gray)
            brightness_score = 1.0 - abs(brightness - 128) / 128.0
            
            # 计算对比度
            contrast = gray.std()
            contrast_score = min(contrast / 64.0, 1.0)
            
            # 检查人脸是否正面（简化检测）
            face_height, face_width = gray.shape
            face_aspect_ratio = face_width / face_height
            frontal_score = 1.0 - abs(face_aspect_ratio - 0.75) / 0.75
            frontal_score = max(0.0, min(1.0, frontal_score))
            
            # 综合评分
            quality_score = (
                sharpness_score * 0.4 +
                brightness_score * 0.2 +
                contrast_score * 0.2 +
                frontal_score * 0.2
            )
            
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            logger.error(f"人脸质量评估失败: {e}")
            return 0.0
    
    def _recognize_face(self, face_encoding: np.ndarray) -> Tuple[Optional[str], float]:
        """识别人脸"""
        if not self.face_encodings_list:
            return None, 0.0
        
        try:
            # 计算与所有已知人脸的距离
            face_distances = face_recognition.face_distance(
                self.face_encodings_list, 
                face_encoding
            )
            
            # 找到最佳匹配
            best_match_index = np.argmin(face_distances)
            best_distance = face_distances[best_match_index]
            
            # 转换距离为置信度
            confidence = max(0.0, 1.0 - best_distance)
            
            # 检查是否超过识别阈值
            if best_distance <= self.face_encoding_tolerance:
                person_id = self.face_names_list[best_match_index]
                return person_id, confidence
            
            return None, 0.0
            
        except Exception as e:
            logger.error(f"人脸识别失败: {e}")
            return None, 0.0
    
    def add_person(self, person_id: str, name: str, photo_path: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加新人员到数据库"""
        try:
            # 加载照片
            image = cv2.imread(photo_path)
            if image is None:
                logger.error(f"无法加载照片: {photo_path}")
                return False
            
            # 转换为RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 检测人脸
            face_locations = face_recognition.face_locations(rgb_image)
            if not face_locations:
                logger.error(f"照片中未检测到人脸: {photo_path}")
                return False
            
            # 获取人脸编码
            face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
            if not face_encodings:
                logger.error(f"无法提取人脸特征: {photo_path}")
                return False
            
            # 创建人员档案
            current_time = time.time()
            
            if person_id in self.known_faces:
                # 更新现有档案
                profile = self.known_faces[person_id]
                profile.encodings.extend(face_encodings)
                profile.photos.append(photo_path)
                if metadata:
                    profile.metadata.update(metadata)
            else:
                # 创建新档案
                profile = PersonProfile(
                    person_id=person_id,
                    name=name,
                    encodings=face_encodings,
                    photos=[photo_path],
                    created_time=current_time,
                    last_seen=None,
                    total_detections=0,
                    metadata=metadata or {}
                )
                self.known_faces[person_id] = profile
            
            # 更新快速查找列表
            for encoding in face_encodings:
                self.face_encodings_list.append(encoding)
                self.face_names_list.append(person_id)
            
            # 保存档案
            self._save_person_profile(profile)
            
            logger.info(f"成功添加人员: {name} (ID: {person_id})")
            return True
            
        except Exception as e:
            logger.error(f"添加人员失败: {e}")
            return False
    
    def _save_person_profile(self, profile: PersonProfile):
        """保存人员档案"""
        profile_path = os.path.join(
            self.face_database_path, 
            'profiles', 
            f"{profile.person_id}.pkl"
        )
        
        try:
            with open(profile_path, 'wb') as f:
                pickle.dump(profile, f)
        except Exception as e:
            logger.error(f"保存人员档案失败: {e}")
    
    def remove_person(self, person_id: str) -> bool:
        """从数据库中删除人员"""
        try:
            if person_id not in self.known_faces:
                logger.warning(f"人员不存在: {person_id}")
                return False
            
            # 从快速查找列表中删除
            indices_to_remove = []
            for i, name in enumerate(self.face_names_list):
                if name == person_id:
                    indices_to_remove.append(i)
            
            for i in reversed(indices_to_remove):
                del self.face_encodings_list[i]
                del self.face_names_list[i]
            
            # 删除档案文件
            profile_path = os.path.join(
                self.face_database_path, 
                'profiles', 
                f"{person_id}.pkl"
            )
            if os.path.exists(profile_path):
                os.remove(profile_path)
            
            # 从内存中删除
            del self.known_faces[person_id]
            
            logger.info(f"成功删除人员: {person_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除人员失败: {e}")
            return False
    
    def get_person_list(self) -> List[Dict[str, Any]]:
        """获取人员列表"""
        person_list = []
        
        for person_id, profile in self.known_faces.items():
            person_info = {
                'person_id': person_id,
                'name': profile.name,
                'encodings_count': len(profile.encodings),
                'photos_count': len(profile.photos),
                'created_time': profile.created_time,
                'last_seen': profile.last_seen,
                'total_detections': profile.total_detections,
                'metadata': profile.metadata
            }
            person_list.append(person_info)
        
        return person_list
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return {
            'total_known_persons': len(self.known_faces),
            'total_face_encodings': len(self.face_encodings_list),
            **self.detection_stats
        }
    
    def update_config(self, config: Dict[str, Any]):
        """更新配置"""
        if 'recognition_threshold' in config:
            self.recognition_threshold = config['recognition_threshold']
        
        if 'quality_threshold' in config:
            self.quality_threshold = config['quality_threshold']
        
        if 'stranger_alert_enabled' in config:
            self.stranger_alert_enabled = config['stranger_alert_enabled']
        
        if 'face_encoding_tolerance' in config:
            self.face_encoding_tolerance = config['face_encoding_tolerance']
        
        logger.info("人脸识别配置已更新")
    
    def get_detector_type(self) -> str:
        return "face_recognition" 