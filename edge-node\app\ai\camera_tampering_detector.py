"""
摄像头遮挡移位检测器
检测摄像头被遮挡、移位、破坏等异常情况
支持背景建模、特征点匹配、画面质量分析等多种检测方法
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque
from enum import Enum

from .detection_engine import BaseDetector, DetectionResult

logger = logging.getLogger(__name__)

class TamperingType(Enum):
    """篡改类型"""
    BLOCKED = "blocked"           # 被遮挡
    MOVED = "moved"              # 移位
    DEFOCUSED = "defocused"      # 失焦
    TOO_DARK = "too_dark"        # 过暗
    TOO_BRIGHT = "too_bright"    # 过亮
    FROZEN = "frozen"            # 画面冻结
    NOISE = "noise"              # 噪声过多
    COLOR_CAST = "color_cast"    # 色偏

@dataclass
class ReferenceFrame:
    """参考帧信息"""
    frame: np.ndarray
    timestamp: float
    keypoints: List[cv2.KeyPoint]
    descriptors: np.ndarray
    histogram: np.ndarray
    mean_brightness: float
    std_deviation: float

class CameraTamperingDetector(BaseDetector):
    """摄像头遮挡移位检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 配置参数
        self.check_interval = config.get('check_interval', 5.0)  # 检查间隔（秒）
        self.reference_update_interval = config.get('reference_update_interval', 300.0)  # 参考帧更新间隔
        self.sensitivity = config.get('sensitivity', 0.8)
        self.movement_threshold = config.get('movement_threshold', 0.3)  # 移位阈值
        self.blocking_threshold = config.get('blocking_threshold', 0.7)  # 遮挡阈值
        
        # 帧缓冲
        self.frame_buffer_size = config.get('frame_buffer_size', 30)
        self.frame_buffer = deque(maxlen=self.frame_buffer_size)
        
        # 参考帧管理
        self.reference_frame: Optional[ReferenceFrame] = None
        self.last_reference_update = 0
        self.stable_frame_count = 0
        self.min_stable_frames = config.get('min_stable_frames', 10)
        
        # 特征检测器
        self.feature_detector = cv2.SIFT_create(nfeatures=500)
        self.matcher = cv2.BFMatcher()
        
        # 背景减除器
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=500, varThreshold=50, detectShadows=True
        )
        
        # 检测状态
        self.last_check_time = 0
        self.tampering_state = {
            'is_tampered': False,
            'tampering_type': None,
            'tampering_start_time': None,
            'confidence': 0.0
        }
        
        # 统计信息
        self.detection_stats = {
            'total_checks': 0,
            'tampering_events': 0,
            'false_alarms': 0,
            'last_tampering_time': None
        }
        
        logger.info("摄像头遮挡移位检测器初始化完成")
    
    def load_model(self) -> bool:
        """加载模型（使用传统CV方法）"""
        return True
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行摄像头异常检测"""
        if not self.enabled:
            return []
        
        current_time = time.time()
        
        # 检查是否需要执行检测
        if current_time - self.last_check_time < self.check_interval:
            return []
        
        self.last_check_time = current_time
        self.detection_stats['total_checks'] += 1
        
        # 添加帧到缓冲区
        self.frame_buffer.append((frame.copy(), current_time))
        
        # 如果缓冲区帧数不足，返回空结果
        if len(self.frame_buffer) < 5:
            return []
        
        results = []
        
        # 初始化或更新参考帧
        if self._should_update_reference_frame(current_time):
            if self._is_frame_stable(frame):
                self._update_reference_frame(frame, current_time)
                return results
        
        # 如果没有参考帧，暂时不进行检测
        if self.reference_frame is None:
            return results
        
        # 执行各种异常检测
        tampering_results = self._detect_tampering(frame, current_time)
        
        # 更新篡改状态
        self._update_tampering_state(tampering_results, current_time)
        
        # 生成告警结果
        if self.tampering_state['is_tampered']:
            result = self._create_tampering_result(current_time)
            if result:
                results.append(result)
        
        return results
    
    def _should_update_reference_frame(self, current_time: float) -> bool:
        """判断是否应该更新参考帧"""
        return (self.reference_frame is None or 
                current_time - self.last_reference_update > self.reference_update_interval)
    
    def _is_frame_stable(self, frame: np.ndarray) -> bool:
        """判断帧是否稳定（无运动物体）"""
        if len(self.frame_buffer) < 3:
            return False
        
        # 计算与前几帧的差异
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        total_diff = 0
        comparison_count = 0
        
        for i in range(1, min(4, len(self.frame_buffer))):
            prev_frame, _ = self.frame_buffer[-i]
            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
            
            diff = cv2.absdiff(gray, prev_gray)
            total_diff += np.mean(diff)
            comparison_count += 1
        
        avg_diff = total_diff / comparison_count if comparison_count > 0 else 0
        
        # 如果平均差异小于阈值，认为帧稳定
        stability_threshold = 5.0
        is_stable = avg_diff < stability_threshold
        
        if is_stable:
            self.stable_frame_count += 1
        else:
            self.stable_frame_count = 0
        
        return self.stable_frame_count >= self.min_stable_frames
    
    def _update_reference_frame(self, frame: np.ndarray, current_time: float):
        """更新参考帧"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 提取特征点
            keypoints, descriptors = self.feature_detector.detectAndCompute(gray, None)
            
            # 计算直方图
            histogram = cv2.calcHist([gray], [0], None, [256], [0, 256])
            histogram = cv2.normalize(histogram, histogram).flatten()
            
            # 计算统计信息
            mean_brightness = np.mean(gray)
            std_deviation = np.std(gray)
            
            self.reference_frame = ReferenceFrame(
                frame=frame.copy(),
                timestamp=current_time,
                keypoints=keypoints,
                descriptors=descriptors,
                histogram=histogram,
                mean_brightness=mean_brightness,
                std_deviation=std_deviation
            )
            
            self.last_reference_update = current_time
            self.stable_frame_count = 0
            
            # 更新背景模型
            self.bg_subtractor.apply(frame)
            
            logger.info(f"参考帧已更新，特征点数量: {len(keypoints)}")
            
        except Exception as e:
            logger.error(f"更新参考帧失败: {e}")
    
    def _detect_tampering(self, frame: np.ndarray, current_time: float) -> Dict[str, Any]:
        """检测各种篡改情况"""
        results = {
            'blocked': self._detect_blocking(frame),
            'moved': self._detect_movement(frame),
            'defocused': self._detect_defocus(frame),
            'brightness_issues': self._detect_brightness_issues(frame),
            'frozen': self._detect_frozen_frame(frame),
            'noise': self._detect_noise(frame),
            'color_cast': self._detect_color_cast(frame)
        }
        
        return results
    
    def _detect_blocking(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测遮挡"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 方法1: 直方图比较
            histogram = cv2.calcHist([gray], [0], None, [256], [0, 256])
            histogram = cv2.normalize(histogram, histogram).flatten()
            
            hist_correlation = cv2.compareHist(
                self.reference_frame.histogram, 
                histogram, 
                cv2.HISTCMP_CORREL
            )
            
            # 方法2: 标准差检查（遮挡时图像变化很小）
            std_dev = np.std(gray)
            std_ratio = std_dev / self.reference_frame.std_deviation if self.reference_frame.std_deviation > 0 else 1.0
            
            # 方法3: 特征点匹配
            keypoints, descriptors = self.feature_detector.detectAndCompute(gray, None)
            
            feature_match_ratio = 0.0
            if descriptors is not None and self.reference_frame.descriptors is not None:
                matches = self.matcher.match(descriptors, self.reference_frame.descriptors)
                feature_match_ratio = len(matches) / max(len(self.reference_frame.descriptors), 1)
            
            # 综合判断
            is_blocked = (
                hist_correlation < (1.0 - self.blocking_threshold) or
                std_ratio < 0.3 or
                feature_match_ratio < 0.1
            )
            
            confidence = 1.0 - min(hist_correlation, std_ratio, feature_match_ratio)
            
            return {
                'detected': is_blocked,
                'confidence': confidence,
                'hist_correlation': hist_correlation,
                'std_ratio': std_ratio,
                'feature_match_ratio': feature_match_ratio
            }
            
        except Exception as e:
            logger.error(f"遮挡检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_movement(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测移位"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 特征点匹配
            keypoints, descriptors = self.feature_detector.detectAndCompute(gray, None)
            
            if descriptors is None or self.reference_frame.descriptors is None:
                return {'detected': False, 'confidence': 0.0}
            
            # 使用FLANN匹配器进行更精确的匹配
            matches = self.matcher.knnMatch(descriptors, self.reference_frame.descriptors, k=2)
            
            # 应用Lowe's ratio test过滤好的匹配
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
            
            if len(good_matches) < 10:
                return {'detected': True, 'confidence': 0.8, 'reason': 'insufficient_matches'}
            
            # 提取匹配点
            src_pts = np.float32([keypoints[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([self.reference_frame.keypoints[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            # 计算单应性矩阵
            homography, mask = cv2.findHomography(
                src_pts, dst_pts, 
                cv2.RANSAC, 
                ransacReprojThreshold=5.0
            )
            
            if homography is None:
                return {'detected': True, 'confidence': 0.7, 'reason': 'no_homography'}
            
            # 分析变换矩阵
            movement_detected, movement_info = self._analyze_homography(homography)
            
            return {
                'detected': movement_detected,
                'confidence': movement_info.get('confidence', 0.0),
                'movement_info': movement_info,
                'good_matches': len(good_matches)
            }
            
        except Exception as e:
            logger.error(f"移位检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _analyze_homography(self, homography: np.ndarray) -> Tuple[bool, Dict[str, Any]]:
        """分析单应性矩阵判断是否移位"""
        try:
            # 提取变换参数
            h = homography
            
            # 计算平移量
            translation_x = h[0, 2]
            translation_y = h[1, 2]
            translation_magnitude = np.sqrt(translation_x**2 + translation_y**2)
            
            # 计算旋转角度
            rotation_angle = np.arctan2(h[1, 0], h[0, 0]) * 180 / np.pi
            
            # 计算缩放比例
            scale_x = np.sqrt(h[0, 0]**2 + h[1, 0]**2)
            scale_y = np.sqrt(h[0, 1]**2 + h[1, 1]**2)
            
            # 判断是否有显著变化
            significant_translation = translation_magnitude > 20  # 像素
            significant_rotation = abs(rotation_angle) > 2  # 度
            significant_scaling = abs(scale_x - 1.0) > 0.1 or abs(scale_y - 1.0) > 0.1
            
            movement_detected = significant_translation or significant_rotation or significant_scaling
            
            # 计算置信度
            confidence = min(
                translation_magnitude / 100.0,
                abs(rotation_angle) / 10.0,
                abs(scale_x - 1.0) + abs(scale_y - 1.0)
            )
            confidence = min(confidence, 1.0)
            
            movement_info = {
                'translation': (translation_x, translation_y),
                'translation_magnitude': translation_magnitude,
                'rotation_angle': rotation_angle,
                'scale': (scale_x, scale_y),
                'confidence': confidence
            }
            
            return movement_detected, movement_info
            
        except Exception as e:
            logger.error(f"分析单应性矩阵失败: {e}")
            return False, {}
    
    def _detect_defocus(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测失焦"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 计算拉普拉斯方差（清晰度指标）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # 与参考帧比较
            if hasattr(self.reference_frame, 'laplacian_var'):
                ref_laplacian_var = self.reference_frame.laplacian_var
            else:
                ref_gray = cv2.cvtColor(self.reference_frame.frame, cv2.COLOR_BGR2GRAY)
                ref_laplacian_var = cv2.Laplacian(ref_gray, cv2.CV_64F).var()
                self.reference_frame.laplacian_var = ref_laplacian_var
            
            # 清晰度下降比例
            clarity_ratio = laplacian_var / ref_laplacian_var if ref_laplacian_var > 0 else 1.0
            
            # 判断是否失焦
            is_defocused = clarity_ratio < 0.5  # 清晰度下降超过50%
            confidence = 1.0 - clarity_ratio if is_defocused else 0.0
            
            return {
                'detected': is_defocused,
                'confidence': confidence,
                'clarity_ratio': clarity_ratio,
                'current_sharpness': laplacian_var,
                'reference_sharpness': ref_laplacian_var
            }
            
        except Exception as e:
            logger.error(f"失焦检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_brightness_issues(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测亮度异常"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            
            ref_brightness = self.reference_frame.mean_brightness
            brightness_diff = abs(mean_brightness - ref_brightness)
            brightness_ratio = brightness_diff / ref_brightness if ref_brightness > 0 else 0
            
            # 判断过暗或过亮
            too_dark = mean_brightness < 30
            too_bright = mean_brightness > 220
            significant_change = brightness_ratio > 0.3
            
            detected = too_dark or too_bright or significant_change
            confidence = max(brightness_ratio, 
                           (30 - mean_brightness) / 30 if too_dark else 0,
                           (mean_brightness - 220) / 35 if too_bright else 0)
            confidence = min(confidence, 1.0)
            
            return {
                'detected': detected,
                'confidence': confidence,
                'too_dark': too_dark,
                'too_bright': too_bright,
                'significant_change': significant_change,
                'current_brightness': mean_brightness,
                'reference_brightness': ref_brightness
            }
            
        except Exception as e:
            logger.error(f"亮度检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_frozen_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测画面冻结"""
        try:
            if len(self.frame_buffer) < 5:
                return {'detected': False, 'confidence': 0.0}
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 与最近几帧比较
            identical_count = 0
            total_comparisons = 0
            
            for i in range(1, min(6, len(self.frame_buffer))):
                prev_frame, _ = self.frame_buffer[-i]
                prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
                
                # 计算结构相似性
                ssim = cv2.matchTemplate(gray, prev_gray, cv2.TM_CCOEFF_NORMED)[0, 0]
                
                if ssim > 0.99:  # 几乎完全相同
                    identical_count += 1
                
                total_comparisons += 1
            
            # 如果大部分帧都相同，认为画面冻结
            frozen_ratio = identical_count / total_comparisons if total_comparisons > 0 else 0
            is_frozen = frozen_ratio > 0.8 and identical_count >= 3
            
            return {
                'detected': is_frozen,
                'confidence': frozen_ratio,
                'identical_frames': identical_count,
                'total_comparisons': total_comparisons
            }
            
        except Exception as e:
            logger.error(f"冻结检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_noise(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测噪声"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 计算噪声水平（高频成分）
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            noise = cv2.absdiff(gray, blurred)
            noise_level = np.mean(noise)
            
            # 与参考帧比较
            ref_gray = cv2.cvtColor(self.reference_frame.frame, cv2.COLOR_BGR2GRAY)
            ref_blurred = cv2.GaussianBlur(ref_gray, (5, 5), 0)
            ref_noise = cv2.absdiff(ref_gray, ref_blurred)
            ref_noise_level = np.mean(ref_noise)
            
            # 噪声增加比例
            noise_ratio = noise_level / ref_noise_level if ref_noise_level > 0 else 1.0
            
            is_noisy = noise_ratio > 2.0  # 噪声增加100%以上
            confidence = min((noise_ratio - 1.0), 1.0) if is_noisy else 0.0
            
            return {
                'detected': is_noisy,
                'confidence': confidence,
                'noise_ratio': noise_ratio,
                'current_noise_level': noise_level,
                'reference_noise_level': ref_noise_level
            }
            
        except Exception as e:
            logger.error(f"噪声检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_color_cast(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测色偏"""
        try:
            # 计算各通道的平均值
            b_mean = np.mean(frame[:, :, 0])
            g_mean = np.mean(frame[:, :, 1])
            r_mean = np.mean(frame[:, :, 2])
            
            # 计算参考帧的通道均值
            ref_b_mean = np.mean(self.reference_frame.frame[:, :, 0])
            ref_g_mean = np.mean(self.reference_frame.frame[:, :, 1])
            ref_r_mean = np.mean(self.reference_frame.frame[:, :, 2])
            
            # 计算色偏
            b_shift = abs(b_mean - ref_b_mean)
            g_shift = abs(g_mean - ref_g_mean)
            r_shift = abs(r_mean - ref_r_mean)
            
            max_shift = max(b_shift, g_shift, r_shift)
            
            # 判断是否有显著色偏
            is_color_cast = max_shift > 20  # 通道差异超过20
            confidence = min(max_shift / 50.0, 1.0) if is_color_cast else 0.0
            
            return {
                'detected': is_color_cast,
                'confidence': confidence,
                'max_shift': max_shift,
                'color_shifts': (b_shift, g_shift, r_shift)
            }
            
        except Exception as e:
            logger.error(f"色偏检测失败: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _update_tampering_state(self, detection_results: Dict[str, Any], current_time: float):
        """更新篡改状态"""
        # 确定最可能的篡改类型和置信度
        max_confidence = 0.0
        detected_type = None
        
        for tampering_type, result in detection_results.items():
            if result.get('detected', False):
                confidence = result.get('confidence', 0.0)
                if confidence > max_confidence:
                    max_confidence = confidence
                    detected_type = tampering_type
        
        # 更新状态
        if max_confidence > self.sensitivity:
            if not self.tampering_state['is_tampered']:
                # 开始篡改
                self.tampering_state['is_tampered'] = True
                self.tampering_state['tampering_type'] = detected_type
                self.tampering_state['tampering_start_time'] = current_time
                self.detection_stats['tampering_events'] += 1
                self.detection_stats['last_tampering_time'] = current_time
            
            self.tampering_state['confidence'] = max_confidence
        else:
            if self.tampering_state['is_tampered']:
                # 篡改结束
                self.tampering_state['is_tampered'] = False
                self.tampering_state['tampering_type'] = None
                self.tampering_state['tampering_start_time'] = None
            
            self.tampering_state['confidence'] = 0.0
    
    def _create_tampering_result(self, current_time: float) -> Optional[DetectionResult]:
        """创建篡改检测结果"""
        if not self.tampering_state['is_tampered']:
            return None
        
        tampering_duration = (
            current_time - self.tampering_state['tampering_start_time']
            if self.tampering_state['tampering_start_time'] else 0
        )
        
        # 只有持续时间超过阈值才报告
        min_duration = 3.0  # 秒
        if tampering_duration < min_duration:
            return None
        
        result = DetectionResult(
            detection_type='camera_tampering',
            confidence=self.tampering_state['confidence'],
            bbox=None,  # 全画面异常
            timestamp=current_time,
            metadata={
                'tampering_type': self.tampering_state['tampering_type'],
                'tampering_duration': tampering_duration,
                'confidence': self.tampering_state['confidence'],
                'detection_stats': self.detection_stats.copy()
            }
        )
        
        return result
    
    def reset_reference_frame(self):
        """重置参考帧"""
        self.reference_frame = None
        self.last_reference_update = 0
        self.stable_frame_count = 0
        logger.info("参考帧已重置")
    
    def get_tampering_status(self) -> Dict[str, Any]:
        """获取篡改状态"""
        return {
            'is_tampered': self.tampering_state['is_tampered'],
            'tampering_type': self.tampering_state['tampering_type'],
            'confidence': self.tampering_state['confidence'],
            'tampering_duration': (
                time.time() - self.tampering_state['tampering_start_time']
                if self.tampering_state['tampering_start_time'] else 0
            ),
            'has_reference_frame': self.reference_frame is not None,
            'detection_stats': self.detection_stats.copy()
        }
    
    def get_detector_type(self) -> str:
        return "camera_tampering" 