# Cerberus 生产环境 Docker Compose 配置
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # PostgreSQL 生产配置
  postgres:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cerberus123}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./infrastructure/database/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis 生产配置
  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data_prod:/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # MQTT 生产配置
  mqtt:
    environment:
      EMQX_LOADED_PLUGINS: "emqx_recon,emqx_retainer,emqx_management,emqx_dashboard"
      EMQX_LISTENER__TCP__EXTERNAL: 1883
      EMQX_LISTENER__SSL__EXTERNAL: 8883
      EMQX_LISTENER__WS__EXTERNAL: 8083
      EMQX_LISTENER__WSS__EXTERNAL: 8084
    volumes:
      - mqtt_data_prod:/opt/emqx/data
      - mqtt_log_prod:/opt/emqx/log
      - ./infrastructure/mqtt/emqx.conf:/opt/emqx/etc/emqx.conf
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # MinIO 生产配置
  minio:
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-cerberus}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-cerberus123}
      MINIO_BROWSER_REDIRECT_URL: ${MINIO_BROWSER_REDIRECT_URL:-http://localhost:9001}
    volumes:
      - minio_data_prod:/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # 中央平台生产配置
  central-platform:
    environment:
      ENVIRONMENT: production
      DEBUG: false
      SECRET_KEY: ${SECRET_KEY}
      DATABASE_URL: postgresql://cerberus:${POSTGRES_PASSWORD:-cerberus123}@postgres:5432/cerberus
      REDIS_URL: redis://:${REDIS_PASSWORD:-}@redis:6379/0
      WORKER_PROCESSES: ${WORKER_PROCESSES:-4}
      WORKER_CONNECTIONS: ${WORKER_CONNECTIONS:-1000}
      LOG_LEVEL: WARNING
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # 边缘节点生产配置
  edge-node-1:
    environment:
      ENVIRONMENT: production
      DEBUG: false
      LOG_LEVEL: WARNING
      DETECTION_CONFIDENCE_THRESHOLD: ${DETECTION_CONFIDENCE_THRESHOLD:-0.6}
      VIDEO_RESOLUTION: ${VIDEO_RESOLUTION:-1920x1080}
      VIDEO_FPS: ${VIDEO_FPS:-25}
    volumes:
      - edge_cache_prod_1:/app/cache
      - edge_models_1:/app/models
      - ./config/cameras.json:/app/config/cameras.json:ro
      - ./config/detectors.json:/app/config/detectors.json:ro
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - central-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # 日志收集器 (可选)
  fluentd:
    image: fluentd:v1.16-1
    volumes:
      - ./infrastructure/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - /var/log:/var/log:ro
      - fluentd_logs:/var/log/fluentd
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # 监控服务 (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # 监控面板 (Grafana)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./infrastructure/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  postgres_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/postgres
  
  redis_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/redis
  
  mqtt_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/mqtt
  
  mqtt_log_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/logs/mqtt
  
  minio_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/minio
  
  edge_cache_prod_1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/edge-cache-1
  
  edge_models_1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/models
  
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/logs/nginx
  
  fluentd_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/logs/fluentd
  
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/prometheus
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/cerberus/data/grafana

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
