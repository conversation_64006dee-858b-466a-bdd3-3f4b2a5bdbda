#!/usr/bin/env python3
"""
Cerberus AI检测器测试脚本
演示遗留物体检测、物体超限检测、人脸识别、跨线计数、摄像头遮挡移位检测功能
"""

import cv2
import numpy as np
import time
import yaml
import logging
from pathlib import Path
import argparse

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config(config_path: str):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def create_test_frame(width=640, height=480, frame_type="normal"):
    """创建测试帧"""
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    
    if frame_type == "normal":
        # 正常场景
        frame[:] = (100, 150, 200)  # 浅蓝色背景
        
        # 绘制一些物体
        cv2.rectangle(frame, (100, 100), (200, 200), (0, 255, 0), -1)  # 绿色矩形
        cv2.circle(frame, (400, 300), 50, (255, 0, 0), -1)  # 蓝色圆形
        
    elif frame_type == "blocked":
        # 被遮挡的场景
        frame[:] = (30, 30, 30)  # 深灰色
        
    elif frame_type == "moved":
        # 移位后的场景
        frame[:] = (100, 150, 200)
        cv2.rectangle(frame, (150, 150), (250, 250), (0, 255, 0), -1)  # 移位的绿色矩形
        cv2.circle(frame, (450, 350), 50, (255, 0, 0), -1)  # 移位的蓝色圆形
        
    elif frame_type == "bright":
        # 过亮场景
        frame[:] = (240, 240, 240)
        
    elif frame_type == "dark":
        # 过暗场景
        frame[:] = (10, 10, 10)
        
    return frame

def test_detection_engine():
    """测试检测引擎"""
    try:
        # 导入检测引擎
        from app.ai.detection_engine import DetectionEngine
        
        # 加载配置
        config_path = "config/ai_detectors_config.yaml"
        if not Path(config_path).exists():
            logger.error(f"配置文件不存在: {config_path}")
            return
            
        config = load_config(config_path)
        ai_config = config.get('ai_detection', {})
        
        # 初始化检测引擎
        logger.info("初始化检测引擎...")
        detection_engine = DetectionEngine(ai_config)
        
        # 创建测试帧序列
        test_scenarios = [
            ("normal", "正常场景"),
            ("blocked", "遮挡场景"),
            ("moved", "移位场景"),
            ("bright", "过亮场景"),
            ("dark", "过暗场景")
        ]
        
        camera_id = "test_camera_01"
        
        for scenario_type, scenario_name in test_scenarios:
            logger.info(f"\n=== 测试场景: {scenario_name} ===")
            
            # 创建测试帧
            frame = create_test_frame(frame_type=scenario_type)
            
            # 执行检测
            start_time = time.time()
            results = detection_engine.detect(camera_id, frame)
            detection_time = time.time() - start_time
            
            logger.info(f"检测耗时: {detection_time:.3f}秒")
            logger.info(f"检测结果数量: {len(results)}")
            
            # 打印检测结果
            for i, result in enumerate(results):
                logger.info(f"  结果 {i+1}:")
                logger.info(f"    类型: {result.detection_type}")
                logger.info(f"    置信度: {result.confidence:.2f}")
                logger.info(f"    边界框: {result.bbox}")
                logger.info(f"    元数据: {result.metadata}")
            
            # 等待一段时间模拟实时检测
            time.sleep(1)
            
        # 获取统计信息
        stats = detection_engine.get_stats()
        logger.info(f"\n=== 检测统计信息 ===")
        logger.info(f"总检测次数: {stats['total_detections']}")
        logger.info(f"平均推理时间: {stats['avg_inference_time']:.3f}秒")
        logger.info(f"当前FPS: {stats['fps']}")
        logger.info(f"活跃检测器: {stats['active_detectors']}")
        logger.info(f"按类型统计: {stats['detections_by_type']}")
        
        # 清理资源
        detection_engine.cleanup()
        logger.info("检测引擎测试完成")
        
    except Exception as e:
        logger.error(f"检测引擎测试失败: {e}")

def test_face_recognition():
    """测试人脸识别功能"""
    try:
        from app.ai.face_recognition_detector import FaceRecognitionDetector
        
        config = {
            'enabled': True,
            'face_database': './test_data/faces/',
            'min_face_size': 50,
            'quality_threshold': 0.6,
            'stranger_alert_enabled': True
        }
        
        logger.info("测试人脸识别检测器...")
        detector = FaceRecognitionDetector(config)
        
        if detector.load_model():
            logger.info("人脸识别模型加载成功")
            
            # 创建包含人脸的测试图像
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            frame[:] = (200, 200, 200)
            
            # 检测人脸
            results = detector.detect(frame)
            logger.info(f"人脸检测结果: {len(results)} 个人脸")
            
            # 获取统计信息
            stats = detector.get_detection_stats()
            logger.info(f"人脸检测统计: {stats}")
        
    except Exception as e:
        logger.error(f"人脸识别测试失败: {e}")

def test_line_crossing():
    """测试跨线计数功能"""
    try:
        from app.ai.line_crossing_counter import LineCrossingCounter
        
        config = {
            'enabled': True,
            'lines': [
                {
                    'line_id': 'test_line',
                    'name': '测试计数线',
                    'start_point': [200, 100],
                    'end_point': [200, 400],
                    'target_classes': ['person'],
                    'bidirectional': True
                }
            ]
        }
        
        logger.info("测试跨线计数检测器...")
        detector = LineCrossingCounter(config)
        
        if detector.load_model():
            logger.info("跨线计数模型加载成功")
            
            # 模拟物体跨线检测
            frame = create_test_frame()
            
            # 模拟目标检测结果
            from app.ai.detection_engine import DetectionResult
            mock_detections = [
                DetectionResult(
                    detection_type='object_detection',
                    confidence=0.9,
                    bbox=(180, 150, 220, 250),
                    timestamp=time.time(),
                    metadata={},
                    class_name='person'
                )
            ]
            
            results = detector.detect(frame, object_detections=mock_detections)
            logger.info(f"跨线检测结果: {len(results)} 个事件")
            
            # 获取统计信息
            stats = detector.get_crossing_statistics()
            logger.info(f"跨线统计: {stats}")
        
    except Exception as e:
        logger.error(f"跨线计数测试失败: {e}")

def test_abandoned_object():
    """测试遗留物体检测功能"""
    try:
        from app.ai.abandoned_object_detector import AbandonedObjectDetector
        
        config = {
            'enabled': True,
            'abandon_threshold': 5.0,  # 测试用短时间
            'target_classes': ['handbag', 'backpack'],
            'min_object_size': 100
        }
        
        logger.info("测试遗留物体检测器...")
        detector = AbandonedObjectDetector(config)
        
        if detector.load_model():
            logger.info("遗留物体检测器加载成功")
            
            # 模拟静止物体
            frame = create_test_frame()
            
            # 模拟目标检测结果
            from app.ai.detection_engine import DetectionResult
            mock_detections = [
                DetectionResult(
                    detection_type='object_detection',
                    confidence=0.8,
                    bbox=(100, 100, 200, 200),
                    timestamp=time.time(),
                    metadata={},
                    class_name='handbag'
                )
            ]
            
            # 多次检测同一物体位置
            for i in range(6):
                results = detector.detect(frame, object_detections=mock_detections)
                logger.info(f"第{i+1}次检测: {len(results)} 个遗留物体")
                time.sleep(1)
            
            # 获取跟踪信息
            tracking_info = detector.get_tracking_info()
            logger.info(f"跟踪信息: {tracking_info}")
        
    except Exception as e:
        logger.error(f"遗留物体检测测试失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Cerberus AI检测器测试')
    parser.add_argument('--test', choices=[
        'all', 'engine', 'face', 'line', 'abandoned'
    ], default='all', help='选择测试类型')
    
    args = parser.parse_args()
    
    logger.info("开始Cerberus AI检测器测试...")
    
    if args.test in ['all', 'engine']:
        test_detection_engine()
    
    if args.test in ['all', 'face']:
        test_face_recognition()
    
    if args.test in ['all', 'line']:
        test_line_crossing()
    
    if args.test in ['all', 'abandoned']:
        test_abandoned_object()
    
    logger.info("所有测试完成!")

if __name__ == "__main__":
    main() 