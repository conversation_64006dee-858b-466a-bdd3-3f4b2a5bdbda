apiVersion: v1
kind: ConfigMap
metadata:
  name: central-platform-config
  namespace: cerberus
data:
  DATABASE_URL: "*******************************************************/cerberus"
  REDIS_URL: "redis://redis-service:6379/0"
  MQTT_BROKER_HOST: "mqtt-service"
  MQTT_BROKER_PORT: "1883"
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_ACCESS_KEY: "cerberus"
  MINIO_SECRET_KEY: "cerberus123"
  MINIO_BUCKET: "cerberus-videos"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: central-platform
  namespace: cerberus
spec:
  replicas: 2
  selector:
    matchLabels:
      app: central-platform
  template:
    metadata:
      labels:
        app: central-platform
    spec:
      containers:
      - name: central-platform
        image: cerberus/central-platform:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: central-platform-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: central-platform-service
  namespace: cerberus
spec:
  selector:
    app: central-platform
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: central-platform-ingress
  namespace: cerberus
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: cerberus.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: central-platform-service
            port:
              number: 8000
