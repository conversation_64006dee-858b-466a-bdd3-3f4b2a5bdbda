{"object_detection": {"enabled": true, "model_path": "models/yolov8n.onnx", "confidence_threshold": 0.5, "nms_threshold": 0.4, "input_size": [640, 640], "target_classes": ["person", "car", "truck", "bicycle", "motorcycle", "bus"], "description": "基于YOLOv8的目标检测"}, "face_recognition": {"enabled": true, "model_path": "models/face_recognition.onnx", "confidence_threshold": 0.7, "face_database": "data/faces/", "min_face_size": 30, "max_face_size": 300, "description": "人脸检测和识别"}, "line_crossing": {"enabled": true, "lines": [{"name": "entrance_line", "start": [100, 200], "end": [300, 200], "direction": "both", "description": "入口越线检测"}, {"name": "exit_line", "start": [400, 300], "end": [600, 300], "direction": "up_to_down", "description": "出口越线检测"}], "track_buffer": 5, "description": "越线检测算法"}, "camera_obstruction": {"enabled": true, "sensitivity": 0.8, "check_interval": 10, "obstruction_threshold": 10, "brightness_min": 20, "brightness_max": 240, "freeze_threshold": 2, "description": "摄像头遮挡和异常检测"}}