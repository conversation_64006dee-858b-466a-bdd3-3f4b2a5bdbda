"""
健康检查端点
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.redis_client import get_redis_client

router = APIRouter()

@router.get("/")
async def health_check():
    """基本健康检查"""
    return {"status": "ok", "message": "Central Platform is running"}

@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """详细健康检查"""
    health_status = {
        "status": "ok",
        "services": {
            "database": "unknown",
            "redis": "unknown",
            "mqtt": "unknown"
        }
    }
    
    # 检查数据库连接
    try:
        await db.execute("SELECT 1")
        health_status["services"]["database"] = "ok"
    except Exception as e:
        health_status["services"]["database"] = f"error: {str(e)}"
        health_status["status"] = "degraded"
    
    # 检查Redis连接
    try:
        redis_client = await get_redis_client()
        await redis_client.ping()
        health_status["services"]["redis"] = "ok"
    except Exception as e:
        health_status["services"]["redis"] = f"error: {str(e)}"
        health_status["status"] = "degraded"
    
    # MQTT检查暂时跳过
    health_status["services"]["mqtt"] = "not_implemented"
    
    return health_status
