# Cerberus Central Platform Dockerfile

FROM hub-mirror.c.163.com/library/python:3.9-slim

# 设置工作目录
WORKDIR /app

# 更换为国内源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r cerberus && useradd -r -g cerberus cerberus

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖 (使用国内源)
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs static/uploads

# 设置权限
RUN chown -R cerberus:cerberus /app

# 切换到非root用户
USER cerberus

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
