# Cerberus AI检测器配置文件
# 支持遗留物体检测、物体超限检测、人脸识别、跨线计数、摄像头遮挡移位检测

ai_detection:
  # 全局配置
  global:
    model_path: "/app/models"
    confidence_threshold: 0.5
    gpu_enabled: true
    max_concurrent_detections: 4
    
  # 检测器配置
  detectors:
    
    # 基础物体检测
    object_detection:
      enabled: true
      model_path: "/app/models/yolov8n.onnx"
      confidence_threshold: 0.5
      class_names_file: "/app/models/coco_classes.txt"
      target_classes:
        - "person"
        - "car"
        - "truck"
        - "bicycle"
        - "motorcycle"
        - "handbag"
        - "backpack"
        - "suitcase"
        - "bottle"
        - "laptop"
      input_size: [640, 640]
      
    # 遗留物体检测
    abandoned_object:
      enabled: true
      abandon_threshold: 30.0  # 秒，物体静止多长时间算遗留
      movement_threshold: 20   # 像素，移动阈值
      min_object_size: 500     # 最小检测面积
      track_retention_time: 300 # 跟踪保持时间
      target_classes:
        - "handbag"
        - "backpack" 
        - "suitcase"
        - "umbrella"
        - "bottle"
        - "laptop"
        - "book"
        - "cell phone"
        - "bicycle"
      exclude_zones:  # 排除区域（垃圾桶、座椅等）
        - [100, 100, 200, 200]  # x1, y1, x2, y2
        - [300, 400, 400, 500]
        
    # 物体超限检测
    overflow_detection:
      enabled: true
      check_interval: 1.0      # 检查间隔（秒）
      smoothing_window: 5      # 平滑窗口大小
      zones:
        - name: "停车区域A"
          polygon: [[100, 100], [400, 100], [400, 300], [100, 300]]
          max_count: 10
          max_area_ratio: 0.8
          target_classes: ["car", "truck"]
          alert_duration: 5.0
          enabled: true
        - name: "人员聚集区"
          polygon: [[200, 200], [600, 200], [600, 500], [200, 500]]
          max_count: 20
          max_area_ratio: 0.6
          target_classes: ["person"]
          alert_duration: 10.0
          enabled: true
          
    # 增强人脸识别
    enhanced_face_recognition:
      enabled: true
      face_database: "/app/data/faces/"
      min_face_size: 50
      max_face_size: 300
      quality_threshold: 0.6
      recognition_threshold: 0.6
      stranger_alert_enabled: true
      face_encoding_tolerance: 0.6
      detection_method: "hog"   # "hog" 或 "cnn"
      
    # 跨线计数检测
    line_crossing_counter:
      enabled: true
      track_retention_time: 5.0
      position_history_size: 10
      iou_threshold: 0.3
      lines:
        - line_id: "entrance_line"
          name: "主入口计数线"
          start_point: [200, 100]
          end_point: [200, 400]
          enabled: true
          bidirectional: true
          target_classes: ["person"]
          min_crossing_confidence: 0.5
          direction_sensitivity: 20
        - line_id: "exit_line"
          name: "出口计数线"
          start_point: [600, 100]
          end_point: [600, 400]
          enabled: true
          bidirectional: true
          target_classes: ["person", "car"]
          min_crossing_confidence: 0.5
          direction_sensitivity: 25
          
    # 摄像头遮挡移位检测
    camera_tampering:
      enabled: true
      check_interval: 5.0               # 检查间隔（秒）
      reference_update_interval: 300.0  # 参考帧更新间隔（秒）
      sensitivity: 0.8                  # 检测敏感度
      movement_threshold: 0.3           # 移位阈值
      blocking_threshold: 0.7           # 遮挡阈值
      frame_buffer_size: 30
      min_stable_frames: 10
      
    # 传统越线检测（简单版本）
    line_crossing:
      enabled: false  # 使用增强版的line_crossing_counter替代
      lines:
        - name: "test_line"
          start: [100, 100]
          end: [500, 100]
          
    # 传统摄像头遮挡检测（简单版本）
    camera_obstruction:
      enabled: false  # 使用增强版的camera_tampering替代
      sensitivity: 0.8
      check_interval: 10

# 性能优化配置
performance:
  # 多线程配置
  threading:
    max_workers: 4
    detection_queue_size: 100
    
  # 内存管理
  memory:
    max_frame_cache: 50
    garbage_collection_interval: 60
    
  # GPU配置
  gpu:
    device_id: 0
    memory_fraction: 0.7
    allow_growth: true

# 日志配置
logging:
  level: "INFO"
  save_detection_images: false
  detection_log_path: "/app/logs/detections/"
  max_log_files: 100
  
# 告警配置  
alerts:
  # 告警阈值
  thresholds:
    abandoned_object_duration: 30.0
    stranger_detection_confidence: 0.8
    tampering_confidence: 0.8
    overflow_duration: 5.0
    
  # 告警频率限制
  rate_limiting:
    max_alerts_per_minute: 10
    cooldown_period: 30  # 秒
    
  # 通知配置
  notifications:
    enable_mqtt: true
    enable_webhook: true
    enable_email: false

# 数据存储配置
storage:
  # 检测结果存储
  detection_results:
    enabled: true
    storage_path: "/app/data/detections/"
    retention_days: 30
    
  # 事件视频录制
  video_recording:
    enabled: true
    record_duration: 10  # 秒
    storage_path: "/app/data/videos/"
    
  # 人脸数据库备份
  face_database_backup:
    enabled: true
    backup_interval: 86400  # 24小时
    backup_path: "/app/backups/faces/"

# 可视化配置
visualization:
  # 绘制检测框
  draw_bboxes: true
  draw_confidence: true
  draw_class_names: true
  
  # 绘制区域和线条
  draw_detection_zones: true
  draw_crossing_lines: true
  draw_exclude_zones: true
  
  # 颜色配置
  colors:
    person: [0, 255, 0]      # 绿色
    vehicle: [255, 0, 0]     # 红色
    abandoned: [255, 255, 0] # 黄色
    stranger: [255, 0, 255]  # 紫色
    alert: [0, 0, 255]       # 蓝色 