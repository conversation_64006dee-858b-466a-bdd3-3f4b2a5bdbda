"""
跨线计数检测器
支持双向计数、多条检测线、轨迹跟踪、统计分析等功能
适用于人员流量统计、车辆计数、出入口监控等场景
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
from enum import Enum

from .detection_engine import BaseDetector, DetectionResult

logger = logging.getLogger(__name__)

class CrossingDirection(Enum):
    """跨线方向"""
    UP_TO_DOWN = "up_to_down"
    DOWN_TO_UP = "down_to_up"
    LEFT_TO_RIGHT = "left_to_right"
    RIGHT_TO_LEFT = "right_to_left"

@dataclass
class DetectionLine:
    """检测线定义"""
    line_id: str
    name: str
    start_point: Tuple[int, int]
    end_point: Tuple[int, int]
    enabled: bool
    bidirectional: bool  # 是否双向计数
    target_classes: List[str]
    min_crossing_confidence: float
    direction_sensitivity: int  # 方向判断敏感度（像素）

@dataclass
class TrackedObject:
    """被跟踪的物体"""
    track_id: int
    class_name: str
    confidence: float
    position_history: deque  # 位置历史
    bbox_history: deque     # 边界框历史
    last_seen: float
    crossed_lines: List[str]  # 已跨越的线
    
@dataclass
class CrossingEvent:
    """跨线事件"""
    event_id: str
    line_id: str
    track_id: int
    class_name: str
    direction: CrossingDirection
    timestamp: float
    crossing_point: Tuple[int, int]
    confidence: float

class LineCrossingCounter(BaseDetector):
    """跨线计数检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 加载检测线配置
        self.lines = self._load_detection_lines(config.get('lines', []))
        
        # 跟踪配置
        self.track_retention_time = config.get('track_retention_time', 5.0)  # 跟踪保持时间
        self.position_history_size = config.get('position_history_size', 10)
        self.iou_threshold = config.get('iou_threshold', 0.3)  # IoU匹配阈值
        
        # 跟踪数据
        self.tracked_objects: Dict[int, TrackedObject] = {}
        self.next_track_id = 1
        
        # 计数统计
        self.crossing_counts: Dict[str, Dict[str, int]] = {}  # {line_id: {direction: count}}
        self.crossing_events: List[CrossingEvent] = []
        self.daily_stats: Dict[str, Dict[str, int]] = {}
        
        # 初始化计数器
        for line in self.lines:
            self.crossing_counts[line.line_id] = {
                'up_to_down': 0,
                'down_to_up': 0,
                'left_to_right': 0,
                'right_to_left': 0,
                'total': 0
            }
        
        logger.info(f"跨线计数检测器初始化完成，共配置 {len(self.lines)} 条检测线")
    
    def _load_detection_lines(self, lines_config: List[Dict]) -> List[DetectionLine]:
        """加载检测线配置"""
        lines = []
        
        for line_config in lines_config:
            line = DetectionLine(
                line_id=line_config.get('line_id', f'line_{len(lines)}'),
                name=line_config.get('name', f'Detection Line {len(lines) + 1}'),
                start_point=tuple(line_config.get('start_point', [0, 0])),
                end_point=tuple(line_config.get('end_point', [100, 100])),
                enabled=line_config.get('enabled', True),
                bidirectional=line_config.get('bidirectional', True),
                target_classes=line_config.get('target_classes', ['person']),
                min_crossing_confidence=line_config.get('min_crossing_confidence', 0.5),
                direction_sensitivity=line_config.get('direction_sensitivity', 20)
            )
            lines.append(line)
        
        return lines
    
    def load_model(self) -> bool:
        """加载模型（使用几何计算，不需要深度学习模型）"""
        return True
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行跨线计数检测"""
        if not self.enabled or not self.lines:
            return []
        
        current_time = time.time()
        object_detections = kwargs.get('object_detections', [])
        
        if not object_detections:
            return []
        
        # 过滤相关的目标检测结果
        relevant_objects = self._filter_relevant_objects(object_detections)
        
        # 更新物体跟踪
        self._update_tracking(relevant_objects, current_time)
        
        # 检测跨线事件
        crossing_results = self._detect_line_crossings(current_time)
        
        # 清理过期的跟踪数据
        self._cleanup_expired_tracks(current_time)
        
        return crossing_results
    
    def _filter_relevant_objects(self, detections: List[DetectionResult]) -> List[DetectionResult]:
        """过滤相关的目标检测结果"""
        relevant = []
        
        # 收集所有目标类别
        target_classes = set()
        for line in self.lines:
            if line.enabled:
                target_classes.update(line.target_classes)
        
        for detection in detections:
            if detection.class_name in target_classes and detection.bbox:
                relevant.append(detection)
        
        return relevant
    
    def _update_tracking(self, detections: List[DetectionResult], current_time: float):
        """更新物体跟踪"""
        if not detections:
            return
        
        # 计算检测结果与现有跟踪的匹配
        matches, unmatched_detections, unmatched_tracks = self._match_detections_to_tracks(detections)
        
        # 更新匹配的跟踪
        for detection_idx, track_id in matches:
            detection = detections[detection_idx]
            tracked_obj = self.tracked_objects[track_id]
            
            # 更新跟踪信息
            center_x, center_y = self._get_bbox_center(detection.bbox)
            
            tracked_obj.position_history.append((center_x, center_y, current_time))
            tracked_obj.bbox_history.append((detection.bbox, current_time))
            tracked_obj.confidence = detection.confidence
            tracked_obj.last_seen = current_time
        
        # 创建新的跟踪
        for detection_idx in unmatched_detections:
            detection = detections[detection_idx]
            self._create_new_track(detection, current_time)
    
    def _match_detections_to_tracks(self, detections: List[DetectionResult]) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """将检测结果匹配到现有跟踪"""
        if not self.tracked_objects:
            return [], list(range(len(detections))), []
        
        # 计算IoU矩阵
        detection_boxes = [det.bbox for det in detections]
        track_boxes = []
        track_ids = []
        
        for track_id, tracked_obj in self.tracked_objects.items():
            if tracked_obj.bbox_history:
                latest_bbox, _ = tracked_obj.bbox_history[-1]
                track_boxes.append(latest_bbox)
                track_ids.append(track_id)
        
        if not track_boxes:
            return [], list(range(len(detections))), []
        
        iou_matrix = self._compute_iou_matrix(detection_boxes, track_boxes)
        
        # 简单的贪婪匹配
        matches = []
        used_detections = set()
        used_tracks = set()
        
        for i in range(len(detection_boxes)):
            for j in range(len(track_boxes)):
                if i in used_detections or j in used_tracks:
                    continue
                
                if iou_matrix[i][j] > self.iou_threshold:
                    matches.append((i, track_ids[j]))
                    used_detections.add(i)
                    used_tracks.add(j)
                    break
        
        unmatched_detections = [i for i in range(len(detections)) if i not in used_detections]
        unmatched_tracks = [track_ids[j] for j in range(len(track_ids)) if j not in used_tracks]
        
        return matches, unmatched_detections, unmatched_tracks
    
    def _compute_iou_matrix(self, boxes1: List[Tuple[int, int, int, int]], 
                           boxes2: List[Tuple[int, int, int, int]]) -> np.ndarray:
        """计算IoU矩阵"""
        matrix = np.zeros((len(boxes1), len(boxes2)))
        
        for i, box1 in enumerate(boxes1):
            for j, box2 in enumerate(boxes2):
                matrix[i, j] = self._calculate_iou(box1, box2)
        
        return matrix
    
    def _calculate_iou(self, box1: Tuple[int, int, int, int], 
                      box2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _get_bbox_center(self, bbox: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """获取边界框中心点"""
        x1, y1, x2, y2 = bbox
        return ((x1 + x2) // 2, (y1 + y2) // 2)
    
    def _create_new_track(self, detection: DetectionResult, current_time: float):
        """创建新的跟踪"""
        center_x, center_y = self._get_bbox_center(detection.bbox)
        
        tracked_obj = TrackedObject(
            track_id=self.next_track_id,
            class_name=detection.class_name,
            confidence=detection.confidence,
            position_history=deque(maxlen=self.position_history_size),
            bbox_history=deque(maxlen=self.position_history_size),
            last_seen=current_time,
            crossed_lines=[]
        )
        
        tracked_obj.position_history.append((center_x, center_y, current_time))
        tracked_obj.bbox_history.append((detection.bbox, current_time))
        
        self.tracked_objects[self.next_track_id] = tracked_obj
        self.next_track_id += 1
    
    def _detect_line_crossings(self, current_time: float) -> List[DetectionResult]:
        """检测跨线事件"""
        results = []
        
        for line in self.lines:
            if not line.enabled:
                continue
            
            for track_id, tracked_obj in self.tracked_objects.items():
                # 检查是否为目标类别
                if tracked_obj.class_name not in line.target_classes:
                    continue
                
                # 检查是否已经跨越过这条线
                if line.line_id in tracked_obj.crossed_lines:
                    continue
                
                # 检查轨迹是否跨越检测线
                crossing_info = self._check_trajectory_crossing(tracked_obj, line)
                
                if crossing_info:
                    direction, crossing_point, confidence = crossing_info
                    
                    # 创建跨线事件
                    event = CrossingEvent(
                        event_id=f"{line.line_id}_{track_id}_{int(current_time)}",
                        line_id=line.line_id,
                        track_id=track_id,
                        class_name=tracked_obj.class_name,
                        direction=direction,
                        timestamp=current_time,
                        crossing_point=crossing_point,
                        confidence=confidence
                    )
                    
                    # 记录事件
                    self.crossing_events.append(event)
                    tracked_obj.crossed_lines.append(line.line_id)
                    
                    # 更新计数
                    self._update_crossing_count(line.line_id, direction)
                    
                    # 创建检测结果
                    result = DetectionResult(
                        detection_type='line_crossing',
                        confidence=confidence,
                        bbox=tracked_obj.bbox_history[-1][0] if tracked_obj.bbox_history else None,
                        timestamp=current_time,
                        metadata={
                            'event_id': event.event_id,
                            'line_id': line.line_id,
                            'line_name': line.name,
                            'track_id': track_id,
                            'direction': direction.value,
                            'crossing_point': crossing_point,
                            'object_class': tracked_obj.class_name,
                            'total_count_after': self.crossing_counts[line.line_id]['total']
                        }
                    )
                    results.append(result)
                    
                    logger.info(
                        f"检测到跨线事件: 线路={line.name}, 方向={direction.value}, "
                        f"类别={tracked_obj.class_name}, 计数={self.crossing_counts[line.line_id]['total']}"
                    )
        
        return results
    
    def _check_trajectory_crossing(self, tracked_obj: TrackedObject, 
                                 line: DetectionLine) -> Optional[Tuple[CrossingDirection, Tuple[int, int], float]]:
        """检查轨迹是否跨越检测线"""
        if len(tracked_obj.position_history) < 2:
            return None
        
        # 获取最近的两个位置点
        current_pos = tracked_obj.position_history[-1]
        previous_pos = tracked_obj.position_history[-2]
        
        current_point = (current_pos[0], current_pos[1])
        previous_point = (previous_pos[0], previous_pos[1])
        
        # 检查线段是否相交
        intersection = self._line_segments_intersect(
            previous_point, current_point,
            line.start_point, line.end_point
        )
        
        if not intersection:
            return None
        
        # 确定跨越方向
        direction = self._determine_crossing_direction(
            previous_point, current_point, line, tracked_obj
        )
        
        if direction is None:
            return None
        
        # 计算置信度
        confidence = min(tracked_obj.confidence, 0.9)
        
        # 获取交点
        crossing_point = self._get_intersection_point(
            previous_point, current_point,
            line.start_point, line.end_point
        )
        
        return direction, crossing_point, confidence
    
    def _line_segments_intersect(self, p1: Tuple[int, int], p2: Tuple[int, int],
                               p3: Tuple[int, int], p4: Tuple[int, int]) -> bool:
        """判断两条线段是否相交"""
        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])
        
        return ccw(p1, p3, p4) != ccw(p2, p3, p4) and ccw(p1, p2, p3) != ccw(p1, p2, p4)
    
    def _get_intersection_point(self, p1: Tuple[int, int], p2: Tuple[int, int],
                              p3: Tuple[int, int], p4: Tuple[int, int]) -> Tuple[int, int]:
        """计算两条线段的交点"""
        x1, y1 = p1
        x2, y2 = p2
        x3, y3 = p3
        x4, y4 = p4
        
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return ((x1 + x2) // 2, (y1 + y2) // 2)  # 返回中点
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        
        x = int(x1 + t * (x2 - x1))
        y = int(y1 + t * (y2 - y1))
        
        return (x, y)
    
    def _determine_crossing_direction(self, prev_point: Tuple[int, int], curr_point: Tuple[int, int],
                                    line: DetectionLine, tracked_obj: TrackedObject) -> Optional[CrossingDirection]:
        """确定跨越方向"""
        # 计算线的方向向量
        line_dx = line.end_point[0] - line.start_point[0]
        line_dy = line.end_point[1] - line.start_point[1]
        
        # 计算运动方向向量
        move_dx = curr_point[0] - prev_point[0]
        move_dy = curr_point[1] - prev_point[1]
        
        # 计算叉积来判断方向
        cross_product = line_dx * move_dy - line_dy * move_dx
        
        # 根据线的方向和叉积符号确定跨越方向
        if abs(line_dx) > abs(line_dy):  # 水平线
            if cross_product > 0:
                return CrossingDirection.UP_TO_DOWN
            else:
                return CrossingDirection.DOWN_TO_UP
        else:  # 垂直线
            if cross_product > 0:
                return CrossingDirection.LEFT_TO_RIGHT
            else:
                return CrossingDirection.RIGHT_TO_LEFT
    
    def _update_crossing_count(self, line_id: str, direction: CrossingDirection):
        """更新跨线计数"""
        if line_id in self.crossing_counts:
            self.crossing_counts[line_id][direction.value] += 1
            self.crossing_counts[line_id]['total'] += 1
    
    def _cleanup_expired_tracks(self, current_time: float):
        """清理过期的跟踪数据"""
        expired_tracks = []
        
        for track_id, tracked_obj in self.tracked_objects.items():
            if current_time - tracked_obj.last_seen > self.track_retention_time:
                expired_tracks.append(track_id)
        
        for track_id in expired_tracks:
            del self.tracked_objects[track_id]
    
    def get_crossing_statistics(self) -> Dict[str, Any]:
        """获取跨线统计信息"""
        stats = {}
        
        for line in self.lines:
            line_stats = {
                'line_name': line.name,
                'enabled': line.enabled,
                'counts': self.crossing_counts.get(line.line_id, {}),
                'target_classes': line.target_classes
            }
            stats[line.line_id] = line_stats
        
        return {
            'line_statistics': stats,
            'total_events': len(self.crossing_events),
            'active_tracks': len(self.tracked_objects)
        }
    
    def reset_counts(self, line_id: Optional[str] = None):
        """重置计数"""
        if line_id:
            if line_id in self.crossing_counts:
                for key in self.crossing_counts[line_id]:
                    self.crossing_counts[line_id][key] = 0
                logger.info(f"重置线路 {line_id} 的计数")
        else:
            for line_id in self.crossing_counts:
                for key in self.crossing_counts[line_id]:
                    self.crossing_counts[line_id][key] = 0
            self.crossing_events.clear()
            logger.info("重置所有计数")
    
    def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的跨线事件"""
        recent_events = sorted(
            self.crossing_events[-limit:], 
            key=lambda x: x.timestamp, 
            reverse=True
        )
        
        return [
            {
                'event_id': event.event_id,
                'line_id': event.line_id,
                'track_id': event.track_id,
                'class_name': event.class_name,
                'direction': event.direction.value,
                'timestamp': event.timestamp,
                'crossing_point': event.crossing_point,
                'confidence': event.confidence
            }
            for event in recent_events
        ]
    
    def get_detector_type(self) -> str:
        return "line_crossing_counter" 