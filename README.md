# Cerberus - 分布式智能安防监控平台

**项目代号:** Cerberus (地狱犬，寓意多头守护)
**版本:** 2.0

## 系统概述

Cerberus是一个专为大规模部署而设计的分布式智能安防监控平台。采用边缘-中心（Edge-Central）架构，支持超过100路并发摄像头的稳定运行。

## 系统架构

```
[摄像头] -> [Edge Node] -> [Message Queue & Distributed Storage] -> [Central Platform] -> [Web管理界面]
```

### 核心组件
- **边缘处理节点 (Edge Node):** Python/Flask - 实时视频分析和事件检测
- **中央管理平台 (Central Platform):** FastAPI - 数据聚合、管理和可视化

### 支撑基础设施
- **消息队列:** MQTT Broker (EMQ X)
- **分布式存储:** MinIO (S3兼容)
- **中央数据库:** PostgreSQL

## 技术栈

### 边缘节点
- Python 3.9+, Flask
- OpenCV, ONNX Runtime
- Paho-MQTT, SQLite
- Docker

### 中央平台
- FastAPI, SQLAlchemy
- PostgreSQL, Redis
- Bootstrap 5, Vanilla JavaScript
- Docker, Kubernetes

## 快速开始

### 环境要求
- Docker & Docker Compose
- Python 3.9+
- Node.js 16+ (用于前端构建)

### 开发环境部署

1. 克隆项目
```bash
git clone <repository-url>
cd cerberus
```

2. 启动基础设施
```bash
docker-compose up -d postgres mqtt minio redis
```

3. 启动中央平台
```bash
cd central-platform
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. 启动边缘节点
```bash
cd edge-node
pip install -r requirements.txt
python app.py
```

### 生产环境部署

使用Kubernetes部署：
```bash
kubectl apply -f k8s/
```

## 项目结构

```
cerberus/
├── edge-node/              # 边缘处理节点
│   ├── app/
│   │   ├── core/           # 核心功能模块
│   │   ├── ai/             # AI推理引擎
│   │   ├── video/          # 视频处理
│   │   └── mqtt/           # MQTT通信
│   ├── models/             # AI模型文件
│   ├── config/             # 配置文件
│   └── Dockerfile
├── central-platform/       # 中央管理平台
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心功能
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务服务
│   │   └── web/            # Web界面
│   ├── alembic/            # 数据库迁移
│   └── Dockerfile
├── infrastructure/         # 基础设施配置
│   ├── mqtt/
│   ├── storage/
│   └── database/
├── k8s/                    # Kubernetes部署文件
├── docker-compose.yml      # 开发环境
└── docs/                   # 文档
```

## API文档

中央平台API文档：http://localhost:8000/docs

## 监控和管理

- Web管理界面：http://localhost:8000
- MQTT管理界面：http://localhost:18083
- MinIO管理界面：http://localhost:9001

## 开发指南

### 添加新的AI检测模型
1. 将ONNX模型文件放入 `edge-node/models/`
2. 在 `edge-node/app/ai/` 中实现检测器类
3. 在配置文件中注册新检测器

### 扩展API功能
1. 在 `central-platform/app/api/` 中添加新路由
2. 在 `central-platform/app/services/` 中实现业务逻辑
3. 更新数据模型（如需要）

## 许可证

[MIT License](LICENSE)

## 贡献

欢迎提交Issue和Pull Request。

## 联系方式

- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
