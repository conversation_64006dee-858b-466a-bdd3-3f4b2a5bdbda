"""
用户管理端点
"""

from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def list_users():
    """获取用户列表"""
    return {"message": "Users list endpoint - not implemented yet"}

@router.post("/")
async def create_user():
    """创建用户"""
    return {"message": "Create user endpoint - not implemented yet"}

@router.get("/{user_id}")
async def get_user(user_id: int):
    """获取用户信息"""
    return {"message": f"Get user {user_id} endpoint - not implemented yet"}
