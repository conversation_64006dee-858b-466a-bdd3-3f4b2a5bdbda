#!/bin/bash

# Cerberus 自动备份脚本
# 用法: ./scripts/backup.sh [--type all|db|config|videos] [--retention 7]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
BACKUP_TYPE="all"
RETENTION_DAYS=7
BACKUP_DIR="/opt/cerberus/backups"
DATE=$(date +%Y%m%d_%H%M%S)
COMPRESS=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BACKUP_TYPE="$2"
            shift 2
            ;;
        --retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --no-compress)
            COMPRESS=false
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "用法: $0 [--type all|db|config|videos] [--retention 7] [--dir /path/to/backup] [--no-compress]"
            exit 1
            ;;
    esac
done

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
}

# 检查磁盘空间
check_disk_space() {
    local required_space_gb=5
    local available_space=$(df "$BACKUP_DIR" | awk 'NR==2 {print int($4/1024/1024)}')
    
    if [ "$available_space" -lt "$required_space_gb" ]; then
        log_error "磁盘空间不足，至少需要 ${required_space_gb}GB，当前可用 ${available_space}GB"
        exit 1
    fi
    
    log_info "磁盘空间检查通过，可用空间: ${available_space}GB"
}

# 备份数据库
backup_database() {
    log_info "开始备份数据库..."
    
    local db_backup_file="$BACKUP_DIR/database_$DATE.sql"
    
    # 检查PostgreSQL容器是否运行
    if ! docker-compose ps postgres | grep -q "Up"; then
        log_error "PostgreSQL容器未运行"
        return 1
    fi
    
    # 执行数据库备份
    if docker-compose exec -T postgres pg_dump -U cerberus -h localhost cerberus > "$db_backup_file"; then
        log_success "数据库备份完成: $db_backup_file"
        
        # 压缩备份文件
        if [ "$COMPRESS" = true ]; then
            gzip "$db_backup_file"
            log_success "数据库备份已压缩: ${db_backup_file}.gz"
        fi
        
        return 0
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 备份配置文件
backup_config() {
    log_info "开始备份配置文件..."
    
    local config_backup_file="$BACKUP_DIR/config_$DATE.tar"
    
    # 创建配置文件列表
    local config_files=(
        ".env"
        ".env.prod"
        "docker-compose.yml"
        "docker-compose.prod.yml"
        "config/"
        "infrastructure/"
        "k8s/"
    )
    
    # 检查文件是否存在并创建备份
    local existing_files=()
    for file in "${config_files[@]}"; do
        if [ -e "$file" ]; then
            existing_files+=("$file")
        fi
    done
    
    if [ ${#existing_files[@]} -gt 0 ]; then
        if tar -cf "$config_backup_file" "${existing_files[@]}" 2>/dev/null; then
            log_success "配置文件备份完成: $config_backup_file"
            
            # 压缩备份文件
            if [ "$COMPRESS" = true ]; then
                gzip "$config_backup_file"
                log_success "配置文件备份已压缩: ${config_backup_file}.gz"
            fi
            
            return 0
        else
            log_error "配置文件备份失败"
            return 1
        fi
    else
        log_warning "未找到配置文件"
        return 1
    fi
}

# 备份视频文件
backup_videos() {
    log_info "开始备份视频文件..."
    
    local videos_backup_dir="$BACKUP_DIR/videos_$DATE"
    
    # 检查MinIO容器是否运行
    if ! docker-compose ps minio | grep -q "Up"; then
        log_error "MinIO容器未运行"
        return 1
    fi
    
    # 使用mc命令备份MinIO数据
    if command -v mc >/dev/null 2>&1; then
        # 配置mc客户端
        mc alias set backup-source http://localhost:9000 cerberus cerberus123 >/dev/null 2>&1
        
        # 创建备份目录
        mkdir -p "$videos_backup_dir"
        
        # 同步视频文件
        if mc mirror backup-source/cerberus-videos "$videos_backup_dir" >/dev/null 2>&1; then
            log_success "视频文件备份完成: $videos_backup_dir"
            
            # 压缩备份目录
            if [ "$COMPRESS" = true ]; then
                tar -czf "${videos_backup_dir}.tar.gz" -C "$BACKUP_DIR" "videos_$DATE"
                rm -rf "$videos_backup_dir"
                log_success "视频文件备份已压缩: ${videos_backup_dir}.tar.gz"
            fi
            
            return 0
        else
            log_error "视频文件备份失败"
            return 1
        fi
    else
        log_warning "mc命令未安装，跳过视频文件备份"
        return 1
    fi
}

# 备份Docker卷
backup_volumes() {
    log_info "开始备份Docker卷..."
    
    local volumes_backup_file="$BACKUP_DIR/volumes_$DATE.tar"
    
    # 获取项目的Docker卷
    local volumes=$(docker volume ls --filter "name=$(basename $(pwd))" --format "{{.Name}}")
    
    if [ -n "$volumes" ]; then
        # 创建临时容器来备份卷
        local temp_container="cerberus-backup-$DATE"
        
        # 备份每个卷
        for volume in $volumes; do
            log_info "备份卷: $volume"
            
            # 创建临时容器挂载卷
            docker run --rm -v "$volume":/data -v "$BACKUP_DIR":/backup alpine \
                tar -czf "/backup/volume_${volume}_$DATE.tar.gz" -C /data . >/dev/null 2>&1
        done
        
        log_success "Docker卷备份完成"
        return 0
    else
        log_warning "未找到Docker卷"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    local deleted_count=0
    
    # 清理数据库备份
    deleted_count=$(find "$BACKUP_DIR" -name "database_*.sql*" -mtime +$RETENTION_DAYS -delete -print | wc -l)
    if [ $deleted_count -gt 0 ]; then
        log_info "删除了 $deleted_count 个旧的数据库备份文件"
    fi
    
    # 清理配置备份
    deleted_count=$(find "$BACKUP_DIR" -name "config_*.tar*" -mtime +$RETENTION_DAYS -delete -print | wc -l)
    if [ $deleted_count -gt 0 ]; then
        log_info "删除了 $deleted_count 个旧的配置备份文件"
    fi
    
    # 清理视频备份
    deleted_count=$(find "$BACKUP_DIR" -name "videos_*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; -print | wc -l)
    if [ $deleted_count -gt 0 ]; then
        log_info "删除了 $deleted_count 个旧的视频备份目录"
    fi
    
    # 清理卷备份
    deleted_count=$(find "$BACKUP_DIR" -name "volume_*.tar.gz" -mtime +$RETENTION_DAYS -delete -print | wc -l)
    if [ $deleted_count -gt 0 ]; then
        log_info "删除了 $deleted_count 个旧的卷备份文件"
    fi
    
    log_success "旧备份清理完成"
}

# 生成备份报告
generate_report() {
    local report_file="$BACKUP_DIR/backup_report_$DATE.txt"
    
    {
        echo "Cerberus 备份报告"
        echo "=================="
        echo "备份时间: $(date)"
        echo "备份类型: $BACKUP_TYPE"
        echo "保留天数: $RETENTION_DAYS"
        echo "备份目录: $BACKUP_DIR"
        echo "压缩选项: $COMPRESS"
        echo
        echo "备份文件列表:"
        ls -lh "$BACKUP_DIR"/*_$DATE* 2>/dev/null || echo "无备份文件"
        echo
        echo "磁盘使用情况:"
        df -h "$BACKUP_DIR"
        echo
        echo "备份完成时间: $(date)"
    } > "$report_file"
    
    log_success "备份报告生成: $report_file"
}

# 主备份函数
main() {
    log_info "开始 Cerberus 系统备份 (类型: $BACKUP_TYPE)"
    
    # 前置检查
    create_backup_dir
    check_disk_space
    
    local backup_success=true
    
    # 根据备份类型执行相应备份
    case $BACKUP_TYPE in
        "all")
            backup_database || backup_success=false
            backup_config || backup_success=false
            backup_videos || backup_success=false
            backup_volumes || backup_success=false
            ;;
        "db")
            backup_database || backup_success=false
            ;;
        "config")
            backup_config || backup_success=false
            ;;
        "videos")
            backup_videos || backup_success=false
            ;;
        "volumes")
            backup_volumes || backup_success=false
            ;;
        *)
            log_error "无效的备份类型: $BACKUP_TYPE"
            exit 1
            ;;
    esac
    
    # 清理旧备份
    cleanup_old_backups
    
    # 生成报告
    generate_report
    
    # 检查备份结果
    if [ "$backup_success" = true ]; then
        log_success "备份完成: $DATE"
        exit 0
    else
        log_error "备份过程中出现错误"
        exit 1
    fi
}

# 错误处理
trap 'log_error "备份过程中发生错误"; exit 1' ERR

# 执行主函数
main
