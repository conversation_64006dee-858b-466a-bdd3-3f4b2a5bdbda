"""
分析服务类
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc

from app.core.database import get_db
from app.models.database import Event, Node, Camera
from app.core.redis_client import cache

logger = logging.getLogger(__name__)

class AnalyticsService:
    """统计分析服务"""
    
    def __init__(self):
        self.cache_ttl = 3600  # 1小时缓存
    
    async def generate_daily_report(self) -> Dict[str, Any]:
        """生成日报"""
        try:
            today = datetime.utcnow().date()
            yesterday = today - timedelta(days=1)
            
            cache_key = f"analytics:daily_report:{today}"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                # 今日事件统计
                today_events = await db.execute(
                    select(func.count(Event.id)).where(
                        and_(
                            Event.created_at >= today,
                            Event.created_at < today + timedelta(days=1)
                        )
                    )
                )
                today_count = today_events.scalar()
                
                # 昨日事件统计
                yesterday_events = await db.execute(
                    select(func.count(Event.id)).where(
                        and_(
                            Event.created_at >= yesterday,
                            Event.created_at < today
                        )
                    )
                )
                yesterday_count = yesterday_events.scalar()
                
                # 今日按类型统计
                type_stats = await db.execute(
                    select(Event.event_type, func.count(Event.id))
                    .where(
                        and_(
                            Event.created_at >= today,
                            Event.created_at < today + timedelta(days=1)
                        )
                    )
                    .group_by(Event.event_type)
                )
                today_by_type = dict(type_stats.fetchall())
                
                # 今日按严重程度统计
                severity_stats = await db.execute(
                    select(Event.severity, func.count(Event.id))
                    .where(
                        and_(
                            Event.created_at >= today,
                            Event.created_at < today + timedelta(days=1)
                        )
                    )
                    .group_by(Event.severity)
                )
                today_by_severity = dict(severity_stats.fetchall())
                
                # 节点状态统计
                node_stats = await db.execute(
                    select(Node.status, func.count(Node.id))
                    .group_by(Node.status)
                )
                nodes_by_status = dict(node_stats.fetchall())
                
                report = {
                    "date": today.isoformat(),
                    "events": {
                        "today": today_count,
                        "yesterday": yesterday_count,
                        "change": today_count - yesterday_count,
                        "by_type": today_by_type,
                        "by_severity": today_by_severity
                    },
                    "nodes": {
                        "by_status": nodes_by_status
                    },
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                await cache.set(cache_key, report, self.cache_ttl)
                return report
                
        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return {}
    
    async def get_trend_analysis(self, days: int = 7) -> Dict[str, Any]:
        """获取趋势分析"""
        try:
            cache_key = f"analytics:trend_{days}days"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            end_date = datetime.utcnow().date()
            start_date = end_date - timedelta(days=days)
            
            async for db in get_db():
                # 按日期统计事件数量
                daily_stats = await db.execute(
                    select(
                        func.date(Event.created_at).label('date'),
                        func.count(Event.id).label('count')
                    )
                    .where(
                        and_(
                            Event.created_at >= start_date,
                            Event.created_at < end_date + timedelta(days=1)
                        )
                    )
                    .group_by(func.date(Event.created_at))
                    .order_by(func.date(Event.created_at))
                )
                
                daily_data = []
                for row in daily_stats:
                    daily_data.append({
                        "date": row.date.isoformat(),
                        "count": row.count
                    })
                
                # 按小时统计（最近24小时）
                last_24h = datetime.utcnow() - timedelta(hours=24)
                hourly_stats = await db.execute(
                    select(
                        func.extract('hour', Event.created_at).label('hour'),
                        func.count(Event.id).label('count')
                    )
                    .where(Event.created_at >= last_24h)
                    .group_by(func.extract('hour', Event.created_at))
                    .order_by(func.extract('hour', Event.created_at))
                )
                
                hourly_data = []
                for row in hourly_stats:
                    hourly_data.append({
                        "hour": int(row.hour),
                        "count": row.count
                    })
                
                trend_analysis = {
                    "period": f"{days} days",
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "daily_trends": daily_data,
                    "hourly_trends_24h": hourly_data,
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                await cache.set(cache_key, trend_analysis, self.cache_ttl)
                return trend_analysis
                
        except Exception as e:
            logger.error(f"Error getting trend analysis: {e}")
            return {}
    
    async def get_node_performance(self) -> Dict[str, Any]:
        """获取节点性能分析"""
        try:
            cache_key = "analytics:node_performance"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                # 每个节点的事件数量
                node_event_stats = await db.execute(
                    select(
                        Node.node_id,
                        Node.name,
                        func.count(Event.id).label('event_count')
                    )
                    .outerjoin(Event, Node.id == Event.node_id)
                    .group_by(Node.id, Node.node_id, Node.name)
                    .order_by(desc(func.count(Event.id)))
                )
                
                node_performance = []
                for row in node_event_stats:
                    node_performance.append({
                        "node_id": row.node_id,
                        "name": row.name,
                        "event_count": row.event_count
                    })
                
                # 节点在线时间统计（简化版本）
                online_nodes = await db.execute(
                    select(func.count(Node.id)).where(Node.status == "online")
                )
                total_nodes = await db.execute(select(func.count(Node.id)))
                
                performance_data = {
                    "node_event_stats": node_performance,
                    "availability": {
                        "online_nodes": online_nodes.scalar(),
                        "total_nodes": total_nodes.scalar(),
                        "availability_rate": (online_nodes.scalar() / max(total_nodes.scalar(), 1)) * 100
                    },
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                await cache.set(cache_key, performance_data, self.cache_ttl)
                return performance_data
                
        except Exception as e:
            logger.error(f"Error getting node performance: {e}")
            return {}
    
    async def get_event_type_analysis(self, days: int = 30) -> Dict[str, Any]:
        """获取事件类型分析"""
        try:
            cache_key = f"analytics:event_types_{days}days"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            start_date = datetime.utcnow() - timedelta(days=days)
            
            async for db in get_db():
                # 事件类型统计
                type_stats = await db.execute(
                    select(
                        Event.event_type,
                        func.count(Event.id).label('count'),
                        func.avg(Event.confidence).label('avg_confidence')
                    )
                    .where(Event.created_at >= start_date)
                    .group_by(Event.event_type)
                    .order_by(desc(func.count(Event.id)))
                )
                
                type_analysis = []
                total_events = 0
                for row in type_stats:
                    count = row.count
                    total_events += count
                    type_analysis.append({
                        "event_type": row.event_type,
                        "count": count,
                        "avg_confidence": float(row.avg_confidence) if row.avg_confidence else 0.0
                    })
                
                # 计算百分比
                for item in type_analysis:
                    item["percentage"] = (item["count"] / max(total_events, 1)) * 100
                
                analysis_data = {
                    "period_days": days,
                    "total_events": total_events,
                    "event_types": type_analysis,
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                await cache.set(cache_key, analysis_data, self.cache_ttl)
                return analysis_data
                
        except Exception as e:
            logger.error(f"Error getting event type analysis: {e}")
            return {}
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        try:
            cache_key = "analytics:system_overview"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                # 总体统计
                total_nodes = await db.execute(select(func.count(Node.id)))
                online_nodes = await db.execute(
                    select(func.count(Node.id)).where(Node.status == "online")
                )
                total_cameras = await db.execute(select(func.count(Camera.id)))
                active_cameras = await db.execute(
                    select(func.count(Camera.id)).where(Camera.status == "active")
                )
                total_events = await db.execute(select(func.count(Event.id)))
                
                # 今日事件
                today = datetime.utcnow().date()
                today_events = await db.execute(
                    select(func.count(Event.id)).where(
                        and_(
                            Event.created_at >= today,
                            Event.created_at < today + timedelta(days=1)
                        )
                    )
                )
                
                # 未处理事件
                pending_events = await db.execute(
                    select(func.count(Event.id)).where(Event.status == "new")
                )
                
                overview = {
                    "nodes": {
                        "total": total_nodes.scalar(),
                        "online": online_nodes.scalar(),
                        "offline": total_nodes.scalar() - online_nodes.scalar()
                    },
                    "cameras": {
                        "total": total_cameras.scalar(),
                        "active": active_cameras.scalar(),
                        "inactive": total_cameras.scalar() - active_cameras.scalar()
                    },
                    "events": {
                        "total": total_events.scalar(),
                        "today": today_events.scalar(),
                        "pending": pending_events.scalar()
                    },
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                await cache.set(cache_key, overview, 300)  # 5分钟缓存
                return overview
                
        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return {}
