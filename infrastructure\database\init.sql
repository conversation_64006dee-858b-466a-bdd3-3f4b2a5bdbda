-- Cerberus 数据库初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE cerberus;

-- 使用数据库
-- \c cerberus;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 首先加载表结构
\i /docker-entrypoint-initdb.d/schema.sql

-- 创建默认角色
INSERT INTO roles (name, description, is_active) VALUES 
('admin', '系统管理员，拥有所有权限', true),
('operator', '操作员，可以管理节点和查看事件', true),
('viewer', '查看者，只能查看数据', true)
ON CONFLICT (name) DO NOTHING;

-- 创建默认权限
INSERT INTO permissions (name, description, resource, action) VALUES 
-- 用户管理权限
('user.create', '创建用户', 'user', 'create'),
('user.read', '查看用户', 'user', 'read'),
('user.update', '更新用户', 'user', 'update'),
('user.delete', '删除用户', 'user', 'delete'),

-- 节点管理权限
('node.create', '创建节点', 'node', 'create'),
('node.read', '查看节点', 'node', 'read'),
('node.update', '更新节点', 'node', 'update'),
('node.delete', '删除节点', 'node', 'delete'),
('node.control', '控制节点', 'node', 'control'),

-- 摄像头管理权限
('camera.create', '创建摄像头', 'camera', 'create'),
('camera.read', '查看摄像头', 'camera', 'read'),
('camera.update', '更新摄像头', 'camera', 'update'),
('camera.delete', '删除摄像头', 'camera', 'delete'),
('camera.control', '控制摄像头', 'camera', 'control'),

-- 事件管理权限
('event.read', '查看事件', 'event', 'read'),
('event.update', '更新事件', 'event', 'update'),
('event.delete', '删除事件', 'event', 'delete'),
('event.export', '导出事件', 'event', 'export'),

-- 系统管理权限
('system.read', '查看系统信息', 'system', 'read'),
('system.update', '更新系统设置', 'system', 'update'),
('system.backup', '系统备份', 'system', 'backup'),
('system.restore', '系统恢复', 'system', 'restore')
ON CONFLICT (name) DO NOTHING;

-- 为角色分配权限
-- 管理员拥有所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 操作员权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'operator' AND p.name IN (
    'node.read', 'node.update', 'node.control',
    'camera.read', 'camera.update', 'camera.control',
    'event.read', 'event.update', 'event.export',
    'system.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 查看者权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'viewer' AND p.name IN (
    'node.read', 'camera.read', 'event.read', 'system.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 创建默认管理员用户（密码：admin123）
INSERT INTO users (username, email, hashed_password, full_name, is_active, is_superuser) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflLxQjO', '系统管理员', true, true)
ON CONFLICT (username) DO NOTHING;

-- 为默认管理员分配管理员角色
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'admin' AND r.name = 'admin'
ON CONFLICT (user_id, role_id) DO NOTHING;

-- 创建默认系统设置
INSERT INTO settings (key, value, description, category, data_type, is_public) VALUES 
('system.name', 'Cerberus 安防监控平台', '系统名称', 'system', 'string', true),
('system.version', '2.0.0', '系统版本', 'system', 'string', true),
('system.timezone', 'Asia/Shanghai', '系统时区', 'system', 'string', false),
('system.language', 'zh-CN', '系统语言', 'system', 'string', true),

('notification.email.enabled', 'false', '是否启用邮件通知', 'notification', 'boolean', false),
('notification.webhook.enabled', 'false', '是否启用Webhook通知', 'notification', 'boolean', false),

('storage.retention.events', '90', '事件数据保留天数', 'storage', 'integer', false),
('storage.retention.videos', '30', '视频文件保留天数', 'storage', 'integer', false),
('storage.retention.logs', '7', '日志文件保留天数', 'storage', 'integer', false),

('detection.confidence.threshold', '0.5', '检测置信度阈值', 'detection', 'float', false),
('detection.max.objects', '50', '单帧最大检测对象数', 'detection', 'integer', false),

('ui.theme', 'light', 'UI主题', 'ui', 'string', true),
('ui.page.size', '20', '默认分页大小', 'ui', 'integer', true),
('ui.refresh.interval', '30', '页面刷新间隔（秒）', 'ui', 'integer', true)
ON CONFLICT (key) DO NOTHING;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_events_created_at_desc ON events (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_events_node_camera_type ON events (node_id, camera_id, event_type);
CREATE INDEX IF NOT EXISTS idx_nodes_status_heartbeat ON nodes (status, last_heartbeat);
CREATE INDEX IF NOT EXISTS idx_cameras_node_status ON cameras (node_id, status);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs (user_id, action, created_at);

-- 创建视图
CREATE OR REPLACE VIEW v_node_summary AS
SELECT 
    n.id,
    n.node_id,
    n.name,
    n.status,
    n.last_heartbeat,
    COUNT(c.id) as camera_count,
    COUNT(CASE WHEN c.status = 'active' THEN 1 END) as active_camera_count,
    COUNT(e.id) as event_count_today
FROM nodes n
LEFT JOIN cameras c ON n.id = c.node_id
LEFT JOIN events e ON n.id = e.node_id AND DATE(e.created_at) = CURRENT_DATE
GROUP BY n.id, n.node_id, n.name, n.status, n.last_heartbeat;

CREATE OR REPLACE VIEW v_event_summary AS
SELECT 
    DATE(created_at) as event_date,
    event_type,
    COUNT(*) as event_count,
    AVG(confidence) as avg_confidence
FROM events
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at), event_type
ORDER BY event_date DESC, event_type;

-- 创建函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE 'alembic%'
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS trigger_update_updated_at ON %I', t);
        EXECUTE format('CREATE TRIGGER trigger_update_updated_at 
                       BEFORE UPDATE ON %I 
                       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', t);
    END LOOP;
END;
$$;
