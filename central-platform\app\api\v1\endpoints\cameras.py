"""
摄像头管理端点
"""

from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def list_cameras():
    """获取摄像头列表"""
    return {"message": "Cameras list endpoint - not implemented yet"}

@router.post("/")
async def create_camera():
    """创建摄像头"""
    return {"message": "Create camera endpoint - not implemented yet"}

@router.get("/{camera_id}")
async def get_camera(camera_id: int):
    """获取摄像头信息"""
    return {"message": f"Get camera {camera_id} endpoint - not implemented yet"}
