# Cerberus 部署指南

## 系统要求

### 硬件要求

#### 中央管理平台
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上SSD
- **网络**: 千兆网卡

#### 边缘处理节点
- **CPU**: 2核心以上（支持AVX指令集）
- **内存**: 4GB以上
- **存储**: 50GB以上
- **GPU**: 可选，支持CUDA的显卡可提升AI推理性能

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Kubernetes**: 1.24+ (生产环境)
- **Python**: 3.9+

## 快速部署

### 1. 开发环境部署

```bash
# 克隆项目
git clone <repository-url>
cd cerberus

# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env

# 启动基础设施服务
docker-compose up -d postgres redis mqtt minio

# 等待服务启动完成
sleep 30

# 启动中央平台
cd central-platform
pip install -r requirements.txt
alembic upgrade head
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动边缘节点（新终端）
cd edge-node
pip install -r requirements.txt
python app.py
```

### 2. Docker Compose 部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f central-platform
docker-compose logs -f edge-node-1
```

### 3. Kubernetes 部署

```bash
# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 部署基础设施
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/redis.yaml
kubectl apply -f k8s/mqtt.yaml
kubectl apply -f k8s/minio.yaml

# 等待基础设施就绪
kubectl wait --for=condition=ready pod -l app=postgres -n cerberus --timeout=300s

# 部署应用
kubectl apply -f k8s/central-platform.yaml
kubectl apply -f k8s/edge-node.yaml

# 查看部署状态
kubectl get pods -n cerberus
kubectl get services -n cerberus
```

## 生产环境部署

### 1. 环境准备

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash cerberus
sudo usermod -aG docker cerberus

# 创建目录结构
sudo mkdir -p /opt/cerberus/{data,logs,config,models}
sudo chown -R cerberus:cerberus /opt/cerberus
```

### 2. 数据库配置

```bash
# 安装PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE cerberus;
CREATE USER cerberus WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE cerberus TO cerberus;
\q

# 配置PostgreSQL
sudo vim /etc/postgresql/14/main/postgresql.conf
# 修改以下配置：
# listen_addresses = '*'
# max_connections = 200
# shared_buffers = 256MB

sudo vim /etc/postgresql/14/main/pg_hba.conf
# 添加：
# host cerberus cerberus 0.0.0.0/0 md5

sudo systemctl restart postgresql
```

### 3. Redis配置

```bash
# 安装Redis
sudo apt install redis-server

# 配置Redis
sudo vim /etc/redis/redis.conf
# 修改以下配置：
# bind 0.0.0.0
# requirepass your-secure-password
# maxmemory 1gb
# maxmemory-policy allkeys-lru

sudo systemctl restart redis-server
```

### 4. MQTT Broker配置

```bash
# 安装EMQ X
wget https://www.emqx.com/en/downloads/broker/5.1.0/emqx-5.1.0-ubuntu20.04-amd64.deb
sudo dpkg -i emqx-5.1.0-ubuntu20.04-amd64.deb

# 启动EMQ X
sudo systemctl start emqx
sudo systemctl enable emqx

# 配置EMQ X
# 访问 http://localhost:18083 进行配置
# 默认用户名: admin, 密码: public
```

### 5. MinIO配置

```bash
# 下载MinIO
wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio
sudo mv minio /usr/local/bin/

# 创建数据目录
sudo mkdir -p /opt/minio/data
sudo chown cerberus:cerberus /opt/minio/data

# 创建systemd服务
sudo vim /etc/systemd/system/minio.service
```

### 6. 应用部署

```bash
# 切换到cerberus用户
sudo su - cerberus

# 克隆代码
git clone <repository-url> /opt/cerberus/app
cd /opt/cerberus/app

# 配置环境变量
cp .env.example .env
vim .env

# 构建Docker镜像
docker build -t cerberus/central-platform:latest central-platform/
docker build -t cerberus/edge-node:latest edge-node/

# 使用Docker Compose部署
docker-compose -f docker-compose.prod.yml up -d
```

## 配置说明

### 1. 数据库配置

```yaml
# alembic.ini
[alembic]
sqlalchemy.url = postgresql://cerberus:password@localhost:5432/cerberus
```

### 2. MQTT主题配置

```
cerberus/nodes/{node_id}/status      # 节点状态
cerberus/events/{node_id}/{camera_id} # 事件数据
cerberus/config/{node_id}/command    # 配置命令
```

### 3. MinIO存储桶配置

```bash
# 创建存储桶
mc alias set myminio http://localhost:9000 cerberus cerberus123
mc mb myminio/cerberus-videos
mc policy set public myminio/cerberus-videos
```

## 监控和维护

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health
curl http://localhost:5000/health

# 检查数据库连接
psql -h localhost -U cerberus -d cerberus -c "SELECT 1;"

# 检查Redis连接
redis-cli ping

# 检查MQTT连接
mosquitto_pub -h localhost -t test -m "hello"
```

### 2. 日志管理

```bash
# 查看应用日志
tail -f /opt/cerberus/logs/central_platform.log
tail -f /opt/cerberus/logs/edge_node.log

# 查看系统日志
journalctl -u cerberus-central -f
journalctl -u cerberus-edge -f
```

### 3. 备份策略

```bash
# 数据库备份
pg_dump -h localhost -U cerberus cerberus > backup_$(date +%Y%m%d).sql

# 配置文件备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/cerberus/config/

# 视频文件备份
rsync -av /opt/cerberus/data/videos/ backup_server:/backup/cerberus/videos/
```

## 故障排除

### 1. 常见问题

**问题**: 节点无法连接到中央平台
**解决**: 检查网络连接、防火墙设置、MQTT配置

**问题**: 视频录制失败
**解决**: 检查存储空间、权限设置、摄像头连接

**问题**: AI检测不工作
**解决**: 检查模型文件、GPU驱动、内存使用

### 2. 性能优化

```bash
# 数据库优化
sudo vim /etc/postgresql/14/main/postgresql.conf
# shared_buffers = 25% of RAM
# effective_cache_size = 75% of RAM
# work_mem = 4MB

# 系统优化
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.rmem_max=134217728' >> /etc/sysctl.conf
sysctl -p
```

## 安全配置

### 1. 网络安全

```bash
# 配置防火墙
sudo ufw allow 22/tcp
sudo ufw allow 8000/tcp
sudo ufw allow 1883/tcp
sudo ufw enable
```

### 2. SSL/TLS配置

```bash
# 生成SSL证书
sudo certbot --nginx -d cerberus.yourdomain.com

# 配置Nginx反向代理
sudo vim /etc/nginx/sites-available/cerberus
```

### 3. 访问控制

```yaml
# 配置RBAC权限
roles:
  - admin: 所有权限
  - operator: 节点管理、事件查看
  - viewer: 只读权限
```
