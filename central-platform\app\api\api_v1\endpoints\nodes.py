"""
节点管理端点
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.models.database import Node

router = APIRouter()

@router.get("/")
async def list_nodes(db: AsyncSession = Depends(get_db)):
    """获取所有节点列表"""
    try:
        result = await db.execute(select(Node))
        nodes = result.scalars().all()
        return [
            {
                "id": node.id,
                "node_id": node.node_id,
                "name": node.name,
                "status": node.status,
                "location": node.location,
                "ip_address": node.ip_address,
                "last_heartbeat": node.last_heartbeat.isoformat() if node.last_heartbeat else None,
                "created_at": node.created_at.isoformat(),
                "updated_at": node.updated_at.isoformat()
            }
            for node in nodes
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes: {str(e)}")

@router.get("/{node_id}")
async def get_node(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取特定节点信息"""
    try:
        result = await db.execute(select(Node).where(Node.node_id == node_id))
        node = result.scalar_one_or_none()
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "id": node.id,
            "node_id": node.node_id,
            "name": node.name,
            "description": node.description,
            "location": node.location,
            "ip_address": node.ip_address,
            "status": node.status,
            "last_heartbeat": node.last_heartbeat.isoformat() if node.last_heartbeat else None,
            "version": node.version,
            "config": node.config,
            "metadata": node.metadata,
            "created_at": node.created_at.isoformat(),
            "updated_at": node.updated_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node: {str(e)}")

@router.get("/{node_id}/status")
async def get_node_status(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取节点状态"""
    try:
        result = await db.execute(select(Node.status, Node.last_heartbeat).where(Node.node_id == node_id))
        node_data = result.first()
        
        if not node_data:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "node_id": node_id,
            "status": node_data.status,
            "last_heartbeat": node_data.last_heartbeat.isoformat() if node_data.last_heartbeat else None
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node status: {str(e)}")
