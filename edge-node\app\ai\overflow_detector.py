"""
物体超限检测器
检测指定区域内物体数量超过限制或占用面积超过阈值的情况
适用于停车场满位检测、人员聚集检测、物品堆积检测等场景
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque

from .detection_engine import BaseDetector, DetectionResult

logger = logging.getLogger(__name__)

@dataclass
class DetectionZone:
    """检测区域定义"""
    name: str
    polygon: List[Tuple[int, int]]  # 多边形顶点
    max_count: int  # 最大物体数量
    max_area_ratio: float  # 最大面积占比 (0.0-1.0)
    target_classes: List[str]  # 目标类别
    alert_duration: float  # 持续时间阈值（秒）
    enabled: bool = True

@dataclass
class ZoneStatus:
    """区域状态"""
    current_count: int
    current_area_ratio: float
    objects_in_zone: List[Dict]
    is_overflow: bool
    overflow_start_time: Optional[float]
    last_check_time: float

class OverflowDetector(BaseDetector):
    """物体超限检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 配置参数
        self.check_interval = config.get('check_interval', 1.0)  # 检查间隔（秒）
        self.smoothing_window = config.get('smoothing_window', 5)  # 平滑窗口大小
        
        # 加载检测区域配置
        self.zones = self._load_detection_zones(config.get('zones', []))
        
        # 区域状态跟踪
        self.zone_status: Dict[str, ZoneStatus] = {}
        self.zone_history: Dict[str, deque] = {}
        
        # 初始化区域状态
        for zone in self.zones:
            self.zone_status[zone.name] = ZoneStatus(
                current_count=0,
                current_area_ratio=0.0,
                objects_in_zone=[],
                is_overflow=False,
                overflow_start_time=None,
                last_check_time=0.0
            )
            self.zone_history[zone.name] = deque(maxlen=self.smoothing_window)
        
        self.last_detection_time = 0
        
        logger.info(f"物体超限检测器初始化完成，共配置 {len(self.zones)} 个检测区域")
    
    def _load_detection_zones(self, zones_config: List[Dict]) -> List[DetectionZone]:
        """加载检测区域配置"""
        zones = []
        
        for zone_config in zones_config:
            zone = DetectionZone(
                name=zone_config.get('name', f'zone_{len(zones)}'),
                polygon=zone_config.get('polygon', []),
                max_count=zone_config.get('max_count', 10),
                max_area_ratio=zone_config.get('max_area_ratio', 0.8),
                target_classes=zone_config.get('target_classes', ['person', 'car']),
                alert_duration=zone_config.get('alert_duration', 5.0),
                enabled=zone_config.get('enabled', True)
            )
            zones.append(zone)
        
        return zones
    
    def load_model(self) -> bool:
        """加载模型（使用几何计算，不需要深度学习模型）"""
        return True
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行物体超限检测"""
        if not self.enabled or not self.zones:
            return []
        
        current_time = time.time()
        
        # 检查是否需要执行检测（按间隔检测）
        if current_time - self.last_detection_time < self.check_interval:
            return []
        
        self.last_detection_time = current_time
        
        object_detections = kwargs.get('object_detections', [])
        if not object_detections:
            return []
        
        results = []
        frame_height, frame_width = frame.shape[:2]
        
        # 检查每个区域
        for zone in self.zones:
            if not zone.enabled:
                continue
            
            zone_results = self._check_zone_overflow(
                zone, object_detections, current_time, frame_width, frame_height
            )
            results.extend(zone_results)
        
        return results
    
    def _check_zone_overflow(self, zone: DetectionZone, detections: List[DetectionResult], 
                           current_time: float, frame_width: int, frame_height: int) -> List[DetectionResult]:
        """检查指定区域的超限情况"""
        results = []
        
        # 过滤在区域内的目标类别物体
        objects_in_zone = self._filter_objects_in_zone(zone, detections)
        
        # 计算区域面积
        zone_area = self._calculate_polygon_area(zone.polygon)
        
        # 计算物体总面积
        total_object_area = sum(
            self._calculate_bbox_area(obj.bbox) for obj in objects_in_zone if obj.bbox
        )
        
        # 计算面积占比
        area_ratio = total_object_area / zone_area if zone_area > 0 else 0.0
        
        # 更新区域状态
        status = self.zone_status[zone.name]
        status.current_count = len(objects_in_zone)
        status.current_area_ratio = area_ratio
        status.objects_in_zone = [
            {
                'class_name': obj.class_name,
                'confidence': obj.confidence,
                'bbox': obj.bbox,
                'area': self._calculate_bbox_area(obj.bbox) if obj.bbox else 0
            }
            for obj in objects_in_zone
        ]
        status.last_check_time = current_time
        
        # 添加到历史记录（用于平滑）
        self.zone_history[zone.name].append({
            'timestamp': current_time,
            'count': status.current_count,
            'area_ratio': status.current_area_ratio
        })
        
        # 计算平滑后的值
        smoothed_count = self._get_smoothed_value('count', zone.name)
        smoothed_area_ratio = self._get_smoothed_value('area_ratio', zone.name)
        
        # 检查是否超限
        count_overflow = smoothed_count > zone.max_count
        area_overflow = smoothed_area_ratio > zone.max_area_ratio
        
        is_overflow = count_overflow or area_overflow
        
        # 处理超限状态变化
        if is_overflow and not status.is_overflow:
            # 开始超限
            status.is_overflow = True
            status.overflow_start_time = current_time
            
        elif not is_overflow and status.is_overflow:
            # 超限结束
            status.is_overflow = False
            status.overflow_start_time = None
        
        # 生成告警（需要持续超限一定时间）
        if (status.is_overflow and status.overflow_start_time and 
            current_time - status.overflow_start_time >= zone.alert_duration):
            
            overflow_duration = current_time - status.overflow_start_time
            
            # 数量超限告警
            if count_overflow:
                result = DetectionResult(
                    detection_type='count_overflow',
                    confidence=0.9,
                    bbox=self._get_zone_bbox(zone.polygon),
                    timestamp=current_time,
                    metadata={
                        'zone_name': zone.name,
                        'current_count': status.current_count,
                        'max_count': zone.max_count,
                        'overflow_duration': overflow_duration,
                        'target_classes': zone.target_classes,
                        'objects': status.objects_in_zone,
                        'overflow_type': 'count'
                    }
                )
                results.append(result)
            
            # 面积超限告警
            if area_overflow:
                result = DetectionResult(
                    detection_type='area_overflow',
                    confidence=0.9,
                    bbox=self._get_zone_bbox(zone.polygon),
                    timestamp=current_time,
                    metadata={
                        'zone_name': zone.name,
                        'current_area_ratio': status.current_area_ratio,
                        'max_area_ratio': zone.max_area_ratio,
                        'overflow_duration': overflow_duration,
                        'target_classes': zone.target_classes,
                        'objects': status.objects_in_zone,
                        'zone_area': zone_area,
                        'total_object_area': total_object_area,
                        'overflow_type': 'area'
                    }
                )
                results.append(result)
            
            # 记录日志
            if count_overflow:
                logger.warning(
                    f"区域 {zone.name} 数量超限: {status.current_count}/{zone.max_count}, "
                    f"持续时间: {overflow_duration:.1f}秒"
                )
            
            if area_overflow:
                logger.warning(
                    f"区域 {zone.name} 面积超限: {status.current_area_ratio:.2%}/{zone.max_area_ratio:.2%}, "
                    f"持续时间: {overflow_duration:.1f}秒"
                )
        
        return results
    
    def _filter_objects_in_zone(self, zone: DetectionZone, detections: List[DetectionResult]) -> List[DetectionResult]:
        """过滤在指定区域内的目标物体"""
        objects_in_zone = []
        
        for detection in detections:
            # 检查类别
            if detection.class_name not in zone.target_classes:
                continue
            
            # 检查位置（使用边界框中心点）
            if detection.bbox:
                x1, y1, x2, y2 = detection.bbox
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                
                if self._point_in_polygon((center_x, center_y), zone.polygon):
                    objects_in_zone.append(detection)
        
        return objects_in_zone
    
    def _point_in_polygon(self, point: Tuple[int, int], polygon: List[Tuple[int, int]]) -> bool:
        """判断点是否在多边形内（射线法）"""
        if len(polygon) < 3:
            return False
        
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _calculate_polygon_area(self, polygon: List[Tuple[int, int]]) -> float:
        """计算多边形面积（鞋带公式）"""
        if len(polygon) < 3:
            return 0.0
        
        area = 0.0
        n = len(polygon)
        
        for i in range(n):
            j = (i + 1) % n
            area += polygon[i][0] * polygon[j][1]
            area -= polygon[j][0] * polygon[i][1]
        
        return abs(area) / 2.0
    
    def _calculate_bbox_area(self, bbox: Tuple[int, int, int, int]) -> int:
        """计算边界框面积"""
        x1, y1, x2, y2 = bbox
        return (x2 - x1) * (y2 - y1)
    
    def _get_zone_bbox(self, polygon: List[Tuple[int, int]]) -> Tuple[int, int, int, int]:
        """获取多边形的外接矩形"""
        if not polygon:
            return (0, 0, 0, 0)
        
        x_coords = [p[0] for p in polygon]
        y_coords = [p[1] for p in polygon]
        
        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
    
    def _get_smoothed_value(self, key: str, zone_name: str) -> float:
        """获取平滑后的值"""
        history = self.zone_history[zone_name]
        if not history:
            return 0.0
        
        values = [record[key] for record in history]
        return sum(values) / len(values)
    
    def get_zone_status(self) -> Dict[str, Any]:
        """获取所有区域状态"""
        status_info = {}
        
        for zone_name, status in self.zone_status.items():
            zone = next((z for z in self.zones if z.name == zone_name), None)
            if zone:
                status_info[zone_name] = {
                    'enabled': zone.enabled,
                    'current_count': status.current_count,
                    'max_count': zone.max_count,
                    'current_area_ratio': status.current_area_ratio,
                    'max_area_ratio': zone.max_area_ratio,
                    'is_overflow': status.is_overflow,
                    'overflow_duration': (
                        time.time() - status.overflow_start_time 
                        if status.overflow_start_time else 0
                    ),
                    'objects_in_zone': status.objects_in_zone,
                    'target_classes': zone.target_classes
                }
        
        return status_info
    
    def update_zone_config(self, zone_name: str, config: Dict[str, Any]):
        """更新区域配置"""
        zone = next((z for z in self.zones if z.name == zone_name), None)
        if zone:
            zone.max_count = config.get('max_count', zone.max_count)
            zone.max_area_ratio = config.get('max_area_ratio', zone.max_area_ratio)
            zone.alert_duration = config.get('alert_duration', zone.alert_duration)
            zone.enabled = config.get('enabled', zone.enabled)
            
            logger.info(f"区域 {zone_name} 配置已更新")
    
    def get_detector_type(self) -> str:
        return "overflow_detection" 