"""
系统信息端点
"""

from fastapi import APIRouter, HTTPException
from app.services.node_service import NodeService
from app.services.event_service import EventService

router = APIRouter()

@router.get("/health")
async def system_health():
    """系统健康检查"""
    try:
        return {
            "status": "healthy",
            "message": "System is running normally"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/info")
async def system_info():
    """获取系统信息"""
    try:
        return {
            "name": "Cerberus Central Platform",
            "version": "2.0.0",
            "description": "分布式智能安防监控平台 - 中央管理平台"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system info: {str(e)}")

@router.get("/metrics")
async def system_metrics():
    """获取系统指标"""
    try:
        node_service = NodeService()
        event_service = EventService()
        
        metrics = {
            "nodes": {
                "total": await node_service.get_total_count(),
                "online": await node_service.get_online_count(),
                "offline": await node_service.get_offline_count()
            },
            "cameras": {
                "total": await node_service.get_total_cameras(),
                "active": await node_service.get_active_cameras()
            },
            "events": {
                "today": await event_service.get_today_count(),
                "this_week": await event_service.get_week_count(),
                "this_month": await event_service.get_month_count()
            }
        }
        
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system metrics: {str(e)}")
