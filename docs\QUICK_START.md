# Cerberus 快速开始指南

## 🚀 5分钟快速部署

### 前提条件

确保您的系统已安装：
- Docker 20.10+
- Docker Compose 2.0+
- Git

### 一键部署

```bash
# 1. 克隆项目
git clone https://github.com/ThomasLXZ/CameraDetection.git
cd CameraDetection

# 2. 一键部署
./scripts/deploy.sh dev

# 3. 等待部署完成 (约3-5分钟)
```

### 访问系统

部署完成后，您可以访问：

- **Web管理界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **MQTT管理**: http://localhost:18083 (admin/public)
- **MinIO管理**: http://localhost:9001 (cerberus/cerberus123)

## 📱 基本使用

### 1. 登录系统

打开浏览器访问 http://localhost:8000

默认管理员账户：
- 用户名: `admin`
- 密码: `admin123`

### 2. 添加边缘节点

1. 进入"节点管理"页面
2. 点击"添加节点"
3. 填写节点信息：
   - 节点ID: `edge-node-2`
   - 名称: `测试节点`
   - 位置: `办公室`
   - IP地址: `*************`

### 3. 配置摄像头

1. 选择已添加的节点
2. 点击"摄像头管理"
3. 添加摄像头：
   - 摄像头ID: `camera-1`
   - 名称: `前门摄像头`
   - RTSP地址: `rtsp://admin:password@*************:554/stream1`
   - 启用检测: ✅

### 4. 查看事件

1. 进入"事件中心"
2. 查看实时检测事件
3. 点击事件可查看详细信息和录像

## 🔧 常用操作

### 查看服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f central-platform
docker-compose logs -f edge-node-1
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart central-platform
```

### 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止并删除数据
docker-compose down -v
```

### 健康检查

```bash
# 执行健康检查
./scripts/health_check.sh

# JSON格式输出
./scripts/health_check.sh --json
```

### 备份数据

```bash
# 完整备份
./scripts/backup.sh --type all

# 仅备份数据库
./scripts/backup.sh --type db

# 仅备份配置
./scripts/backup.sh --type config
```

## 🎯 配置摄像头

### RTSP摄像头配置

编辑 `config/cameras.json`：

```json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "enabled": true,
    "detection_zones": [
      {
        "name": "entrance",
        "polygon": [[0, 0], [100, 0], [100, 100], [0, 100]]
      }
    ],
    "detectors": ["object_detection", "face_recognition"]
  },
  "camera_2": {
    "name": "后门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "enabled": true,
    "detection_zones": [
      {
        "name": "exit",
        "polygon": [[0, 0], [200, 0], [200, 150], [0, 150]]
      }
    ],
    "detectors": ["object_detection", "line_crossing"]
  }
}
```

### 常见摄像头品牌RTSP地址

| 品牌 | RTSP地址格式 |
|------|-------------|
| 海康威视 | `rtsp://admin:password@ip:554/Streaming/Channels/101` |
| 大华 | `rtsp://admin:password@ip:554/cam/realmonitor?channel=1&subtype=0` |
| 宇视 | `rtsp://admin:password@ip:554/video1` |
| 萤石 | `rtsp://admin:password@ip:554/h264/ch1/main/av_stream` |

## 🤖 AI检测配置

### 配置检测器

编辑 `config/detectors.json`：

```json
{
  "object_detection": {
    "model_path": "models/yolov8n.onnx",
    "confidence_threshold": 0.5,
    "nms_threshold": 0.4,
    "target_classes": ["person", "car", "truck", "bicycle"],
    "enabled": true
  },
  "face_recognition": {
    "model_path": "models/face_recognition.onnx",
    "confidence_threshold": 0.7,
    "face_database": "data/faces/",
    "enabled": true
  },
  "line_crossing": {
    "enabled": true,
    "lines": [
      {
        "name": "entrance_line",
        "start": [100, 200],
        "end": [300, 200]
      }
    ]
  }
}
```

### 下载AI模型

```bash
# 下载YOLOv8模型
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.onnx -O edge-node/models/yolov8n.onnx

# 下载人脸识别模型
wget https://github.com/onnx/models/raw/main/vision/body_analysis/arcface/model/arcfaceresnet100-8.onnx -O edge-node/models/face_recognition.onnx
```

## 📊 监控和告警

### 设置告警

编辑 `.env` 文件：

```bash
# 邮件告警
EMAIL_ENABLED=true
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Webhook告警
WEBHOOK_ENABLED=true
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 查看系统指标

访问 http://localhost:8000/metrics 查看系统指标：

```json
{
  "nodes": {
    "total": 2,
    "online": 2,
    "offline": 0
  },
  "cameras": {
    "total": 4,
    "active": 3
  },
  "events": {
    "today": 15,
    "this_week": 89,
    "this_month": 342
  }
}
```

## 🔒 安全配置

### 修改默认密码

```bash
# 修改管理员密码
curl -X POST http://localhost:8000/api/v1/auth/change-password \
  -H "Content-Type: application/json" \
  -d '{"old_password": "admin123", "new_password": "your-new-password"}'

# 修改数据库密码
docker-compose exec postgres psql -U cerberus -c "ALTER USER cerberus PASSWORD 'new-password';"
```

### 启用HTTPS

```bash
# 生成SSL证书
sudo certbot --nginx -d your-domain.com

# 更新nginx配置
sudo vim /etc/nginx/sites-available/cerberus
```

## 🚨 故障排除

### 常见问题

**问题1**: 容器启动失败
```bash
# 查看错误日志
docker-compose logs [service-name]

# 重新构建镜像
docker-compose build --no-cache
```

**问题2**: 摄像头连接失败
```bash
# 测试RTSP连接
ffplay rtsp://admin:password@*************:554/stream1

# 检查网络连接
ping *************
telnet ************* 554
```

**问题3**: AI检测不工作
```bash
# 检查模型文件
ls -la edge-node/models/

# 查看边缘节点日志
docker-compose logs -f edge-node-1
```

### 获取帮助

- **GitHub Issues**: https://github.com/ThomasLXZ/CameraDetection/issues
- **文档**: 查看 `docs/` 目录
- **日志**: 使用 `docker-compose logs` 查看详细日志

## 📈 性能优化

### 调整检测参数

```bash
# 降低检测频率 (提高性能)
DETECTION_INTERVAL=2.0

# 调整置信度阈值
DETECTION_CONFIDENCE_THRESHOLD=0.6

# 调整视频分辨率
VIDEO_RESOLUTION=1280x720
```

### 资源监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
```

## 🎉 下一步

恭喜！您已成功部署并配置了Cerberus系统。

建议下一步：

1. **添加更多摄像头** - 扩展监控范围
2. **配置告警规则** - 及时响应安全事件
3. **优化AI模型** - 提高检测准确性
4. **设置备份策略** - 保护重要数据
5. **部署到生产环境** - 使用Kubernetes进行生产部署

更多详细信息请参考：
- [完整部署指南](DEPLOYMENT_GUIDE.md)
- [项目总结](PROJECT_SUMMARY.md)
