"""
事件服务类
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc

from app.core.database import get_db
from app.models.database import Event, Node, Camera
from app.core.redis_client import cache

logger = logging.getLogger(__name__)

class EventService:
    """事件管理服务"""
    
    def __init__(self):
        self.cache_ttl = 300  # 5分钟缓存
    
    async def get_today_count(self) -> int:
        """获取今日事件数"""
        try:
            cache_key = "events:today_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            today = datetime.utcnow().date()
            tomorrow = today + timedelta(days=1)
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Event.id)).where(
                        and_(
                            Event.created_at >= today,
                            Event.created_at < tomorrow
                        )
                    )
                )
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting today's event count: {e}")
            return 0
    
    async def get_week_count(self) -> int:
        """获取本周事件数"""
        try:
            cache_key = "events:week_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            today = datetime.utcnow().date()
            week_start = today - timedelta(days=today.weekday())
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Event.id)).where(
                        Event.created_at >= week_start
                    )
                )
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting week's event count: {e}")
            return 0
    
    async def get_month_count(self) -> int:
        """获取本月事件数"""
        try:
            cache_key = "events:month_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            today = datetime.utcnow().date()
            month_start = today.replace(day=1)
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Event.id)).where(
                        Event.created_at >= month_start
                    )
                )
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting month's event count: {e}")
            return 0
    
    async def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近事件"""
        try:
            cache_key = f"events:recent_{limit}"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                result = await db.execute(
                    select(Event)
                    .order_by(desc(Event.created_at))
                    .limit(limit)
                )
                events = result.scalars().all()
                
                events_data = [
                    {
                        "id": event.id,
                        "event_id": event.event_id,
                        "event_type": event.event_type,
                        "confidence": event.confidence,
                        "description": event.description,
                        "status": event.status,
                        "severity": event.severity,
                        "created_at": event.created_at.isoformat()
                    }
                    for event in events
                ]
                
                await cache.set(cache_key, events_data, self.cache_ttl)
                return events_data
        except Exception as e:
            logger.error(f"Error getting recent events: {e}")
            return []
    
    async def create_event(self, event_data: Dict[str, Any]) -> Optional[str]:
        """创建新事件"""
        try:
            async for db in get_db():
                new_event = Event(**event_data)
                db.add(new_event)
                await db.commit()
                await db.refresh(new_event)
                
                # 清除相关缓存
                await cache.clear_pattern("events:*")
                
                return new_event.event_id
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            return None
    
    async def get_event_by_id(self, event_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取事件"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(Event).where(Event.event_id == event_id)
                )
                event = result.scalar_one_or_none()
                
                if event:
                    return {
                        "id": event.id,
                        "event_id": event.event_id,
                        "node_id": event.node_id,
                        "camera_id": event.camera_id,
                        "event_type": event.event_type,
                        "confidence": event.confidence,
                        "bbox": event.bbox,
                        "description": event.description,
                        "status": event.status,
                        "severity": event.severity,
                        "metadata": event.event_metadata,
                        "created_at": event.created_at,
                        "updated_at": event.updated_at
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting event {event_id}: {e}")
            return None
    
    async def update_event_status(self, event_id: str, status: str) -> bool:
        """更新事件状态"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(Event).where(Event.event_id == event_id)
                )
                event = result.scalar_one_or_none()
                
                if event:
                    event.status = status
                    await db.commit()
                    
                    # 清除相关缓存
                    await cache.clear_pattern("events:*")
                    
                    return True
                return False
        except Exception as e:
            logger.error(f"Error updating event status for {event_id}: {e}")
            return False
    
    async def get_events_by_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        event_type: Optional[str] = None,
        status: Optional[str] = None,
        severity: Optional[str] = None,
        node_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """根据过滤条件获取事件列表"""
        try:
            async for db in get_db():
                query = select(Event).order_by(desc(Event.created_at))
                
                # 添加过滤条件
                if event_type:
                    query = query.where(Event.event_type == event_type)
                if status:
                    query = query.where(Event.status == status)
                if severity:
                    query = query.where(Event.severity == severity)
                if node_id:
                    # 通过节点ID过滤
                    node_result = await db.execute(select(Node.id).where(Node.node_id == node_id))
                    node_db_id = node_result.scalar_one_or_none()
                    if node_db_id:
                        query = query.where(Event.node_id == node_db_id)
                    else:
                        return []
                if start_date:
                    query = query.where(Event.created_at >= start_date)
                if end_date:
                    query = query.where(Event.created_at <= end_date)
                
                # 分页
                query = query.offset(skip).limit(limit)
                
                result = await db.execute(query)
                events = result.scalars().all()
                
                return [
                    {
                        "id": event.id,
                        "event_id": event.event_id,
                        "event_type": event.event_type,
                        "confidence": event.confidence,
                        "description": event.description,
                        "status": event.status,
                        "severity": event.severity,
                        "bbox": event.bbox,
                        "metadata": event.event_metadata,
                        "created_at": event.created_at.isoformat(),
                        "updated_at": event.updated_at.isoformat()
                    }
                    for event in events
                ]
        except Exception as e:
            logger.error(f"Error getting events by filters: {e}")
            return []
    
    async def cleanup_expired_events(self, days_to_keep: int = 30) -> int:
        """清理过期事件"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Event.id)).where(
                        and_(
                            Event.created_at < cutoff_date,
                            Event.status == "archived"
                        )
                    )
                )
                count_to_delete = result.scalar()
                
                if count_to_delete > 0:
                    # 删除过期事件
                    await db.execute(
                        Event.__table__.delete().where(
                            and_(
                                Event.created_at < cutoff_date,
                                Event.status == "archived"
                            )
                        )
                    )
                    await db.commit()
                    
                    # 清除缓存
                    await cache.clear_pattern("events:*")
                    
                    logger.info(f"Cleaned up {count_to_delete} expired events")
                
                return count_to_delete
        except Exception as e:
            logger.error(f"Error cleaning up expired events: {e}")
            return 0
    
    async def get_event_statistics(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        try:
            async for db in get_db():
                # 按类型统计
                type_result = await db.execute(
                    select(Event.event_type, func.count(Event.id))
                    .group_by(Event.event_type)
                )
                by_type = dict(type_result.fetchall())
                
                # 按状态统计
                status_result = await db.execute(
                    select(Event.status, func.count(Event.id))
                    .group_by(Event.status)
                )
                by_status = dict(status_result.fetchall())
                
                # 按严重程度统计
                severity_result = await db.execute(
                    select(Event.severity, func.count(Event.id))
                    .group_by(Event.severity)
                )
                by_severity = dict(severity_result.fetchall())
                
                return {
                    "by_type": by_type,
                    "by_status": by_status,
                    "by_severity": by_severity
                }
        except Exception as e:
            logger.error(f"Error getting event statistics: {e}")
            return {"by_type": {}, "by_status": {}, "by_severity": {}}
