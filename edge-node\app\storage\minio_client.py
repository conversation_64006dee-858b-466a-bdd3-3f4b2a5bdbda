"""
Cerberus MinIO客户端
负责视频文件的分布式存储和管理
"""

import os
import time
import logging
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import minio
from minio.error import S3Error
import hashlib

logger = logging.getLogger(__name__)

class MinIOClient:
    """MinIO客户端"""
    
    def __init__(self, config):
        self.config = config
        self.client = None
        self.bucket_name = config.MINIO_BUCKET
        self.upload_queue = []
        self.upload_thread = None
        self.running = False
        
        # 统计信息
        self.stats = {
            'files_uploaded': 0,
            'files_failed': 0,
            'bytes_uploaded': 0,
            'last_upload_time': 0,
            'upload_queue_size': 0
        }
        
        logger.info("MinIO客户端初始化完成")
    
    def initialize(self) -> bool:
        """初始化MinIO客户端"""
        try:
            # 创建MinIO客户端
            self.client = minio.Minio(
                self.config.MINIO_ENDPOINT,
                access_key=self.config.MINIO_ACCESS_KEY,
                secret_key=self.config.MINIO_SECRET_KEY,
                secure=self.config.MINIO_SECURE
            )
            
            # 检查连接
            if not self.client.bucket_exists(self.bucket_name):
                # 创建存储桶
                self.client.make_bucket(self.bucket_name)
                logger.info(f"创建存储桶: {self.bucket_name}")
            
            # 启动上传服务
            self._start_upload_service()
            
            logger.info(f"MinIO客户端初始化成功: {self.config.MINIO_ENDPOINT}")
            return True
            
        except Exception as e:
            logger.error(f"MinIO客户端初始化失败: {e}")
            return False
    
    def _start_upload_service(self):
        """启动上传服务"""
        if self.upload_thread and self.upload_thread.is_alive():
            return
        
        self.running = True
        self.upload_thread = threading.Thread(target=self._upload_loop, daemon=True)
        self.upload_thread.start()
        logger.info("MinIO上传服务已启动")
    
    def _upload_loop(self):
        """上传循环"""
        while self.running:
            try:
                if self.upload_queue:
                    # 获取待上传文件
                    upload_task = self.upload_queue.pop(0)
                    self._upload_file_internal(upload_task)
                else:
                    time.sleep(1)  # 队列为空时等待
                    
            except Exception as e:
                logger.error(f"上传循环错误: {e}")
                time.sleep(5)
    
    def upload_file(self, local_path: str, object_name: Optional[str] = None, 
                   metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件（异步）"""
        try:
            if not os.path.exists(local_path):
                logger.error(f"文件不存在: {local_path}")
                return False
            
            if object_name is None:
                object_name = os.path.basename(local_path)
            
            # 添加到上传队列
            upload_task = {
                'local_path': local_path,
                'object_name': object_name,
                'metadata': metadata or {},
                'retry_count': 0,
                'max_retries': 3
            }
            
            self.upload_queue.append(upload_task)
            self.stats['upload_queue_size'] = len(self.upload_queue)
            
            logger.debug(f"文件已加入上传队列: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"添加上传任务失败: {e}")
            return False
    
    def _upload_file_internal(self, upload_task: Dict[str, Any]) -> bool:
        """内部上传文件方法"""
        try:
            local_path = upload_task['local_path']
            object_name = upload_task['object_name']
            metadata = upload_task['metadata']
            
            if not os.path.exists(local_path):
                logger.error(f"上传文件不存在: {local_path}")
                self.stats['files_failed'] += 1
                return False
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(local_path)
            metadata['file_hash'] = file_hash
            metadata['upload_time'] = str(time.time())
            metadata['node_id'] = self.config.NODE_ID
            
            # 上传文件
            file_size = os.path.getsize(local_path)
            
            self.client.fput_object(
                self.bucket_name,
                object_name,
                local_path,
                metadata=metadata
            )
            
            # 更新统计信息
            self.stats['files_uploaded'] += 1
            self.stats['bytes_uploaded'] += file_size
            self.stats['last_upload_time'] = time.time()
            self.stats['upload_queue_size'] = len(self.upload_queue)
            
            logger.info(f"文件上传成功: {object_name} ({file_size} bytes)")
            
            # 删除本地文件（可选）
            if self.config.get('DELETE_AFTER_UPLOAD', False):
                try:
                    os.remove(local_path)
                    logger.debug(f"本地文件已删除: {local_path}")
                except Exception as e:
                    logger.warning(f"删除本地文件失败: {e}")
            
            return True
            
        except S3Error as e:
            logger.error(f"MinIO上传失败: {e}")
            self._handle_upload_retry(upload_task)
            return False
            
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            self._handle_upload_retry(upload_task)
            return False
    
    def _handle_upload_retry(self, upload_task: Dict[str, Any]):
        """处理上传重试"""
        upload_task['retry_count'] += 1
        
        if upload_task['retry_count'] <= upload_task['max_retries']:
            # 重新加入队列
            self.upload_queue.append(upload_task)
            logger.warning(f"上传重试 {upload_task['retry_count']}/{upload_task['max_retries']}: {upload_task['local_path']}")
        else:
            # 超过最大重试次数
            self.stats['files_failed'] += 1
            logger.error(f"上传失败，超过最大重试次数: {upload_task['local_path']}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def upload_video(self, video_path: str, event_id: str, camera_id: str) -> bool:
        """上传事件视频"""
        try:
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return False
            
            # 生成对象名称
            timestamp = datetime.now().strftime('%Y/%m/%d')
            filename = os.path.basename(video_path)
            object_name = f"videos/{timestamp}/{camera_id}/{filename}"
            
            # 添加元数据
            metadata = {
                'event_id': event_id,
                'camera_id': camera_id,
                'node_id': self.config.NODE_ID,
                'content_type': 'video/mp4'
            }
            
            return self.upload_file(video_path, object_name, metadata)
            
        except Exception as e:
            logger.error(f"上传视频失败: {e}")
            return False
    
    def download_file(self, object_name: str, local_path: str) -> bool:
        """下载文件"""
        try:
            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 下载文件
            self.client.fget_object(self.bucket_name, object_name, local_path)
            
            logger.info(f"文件下载成功: {object_name} -> {local_path}")
            return True
            
        except S3Error as e:
            logger.error(f"MinIO下载失败: {e}")
            return False
            
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
            return False
    
    def delete_file(self, object_name: str) -> bool:
        """删除文件"""
        try:
            self.client.remove_object(self.bucket_name, object_name)
            logger.info(f"文件删除成功: {object_name}")
            return True
            
        except S3Error as e:
            logger.error(f"MinIO删除失败: {e}")
            return False
            
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False
    
    def list_files(self, prefix: str = "", limit: int = 100) -> List[Dict[str, Any]]:
        """列出文件"""
        try:
            objects = self.client.list_objects(
                self.bucket_name,
                prefix=prefix,
                recursive=True
            )
            
            files = []
            count = 0
            
            for obj in objects:
                if count >= limit:
                    break
                
                files.append({
                    'name': obj.object_name,
                    'size': obj.size,
                    'last_modified': obj.last_modified.isoformat() if obj.last_modified else None,
                    'etag': obj.etag
                })
                count += 1
            
            return files
            
        except S3Error as e:
            logger.error(f"MinIO列表失败: {e}")
            return []
            
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return []
    
    def get_file_url(self, object_name: str, expires: int = 3600) -> Optional[str]:
        """获取文件预签名URL"""
        try:
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(seconds=expires)
            )
            return url
            
        except S3Error as e:
            logger.error(f"生成预签名URL失败: {e}")
            return None
            
        except Exception as e:
            logger.error(f"获取文件URL失败: {e}")
            return None
    
    def upload_pending_videos(self) -> int:
        """上传待上传的视频文件"""
        try:
            # 扫描本地视频目录
            video_dir = os.path.join(self.config.CACHE_DIR, 'videos')
            if not os.path.exists(video_dir):
                return 0
            
            uploaded_count = 0
            
            for root, dirs, files in os.walk(video_dir):
                for file in files:
                    if file.endswith('.mp4'):
                        local_path = os.path.join(root, file)
                        
                        # 检查文件是否已完成写入（通过修改时间判断）
                        file_mtime = os.path.getmtime(local_path)
                        if time.time() - file_mtime < 60:  # 1分钟内修改的文件跳过
                            continue
                        
                        # 解析文件名获取信息
                        try:
                            # 假设文件名格式：camera_id_event_id_timestamp.mp4
                            parts = file.replace('.mp4', '').split('_')
                            if len(parts) >= 3:
                                camera_id = parts[0]
                                event_id = '_'.join(parts[1:-1])
                                
                                if self.upload_video(local_path, event_id, camera_id):
                                    uploaded_count += 1
                        except Exception as e:
                            logger.error(f"解析视频文件名失败 {file}: {e}")
            
            if uploaded_count > 0:
                logger.info(f"批量上传视频完成: {uploaded_count} 个文件")
            
            return uploaded_count
            
        except Exception as e:
            logger.error(f"批量上传视频失败: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'files_uploaded': self.stats['files_uploaded'],
            'files_failed': self.stats['files_failed'],
            'bytes_uploaded': self.stats['bytes_uploaded'],
            'upload_queue_size': self.stats['upload_queue_size'],
            'last_upload_time': self.stats['last_upload_time'],
            'connected': self.client is not None
        }
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        
        if self.upload_thread and self.upload_thread.is_alive():
            self.upload_thread.join(timeout=10)
        
        logger.info("MinIO客户端已清理")
