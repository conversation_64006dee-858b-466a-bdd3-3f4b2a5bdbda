#!/bin/bash

# Cerberus 系统健康检查脚本
# 用法: ./scripts/health_check.sh [--json] [--alert]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
TIMEOUT=10
JSON_OUTPUT=false
ALERT_MODE=false
WEBHOOK_URL=""
EMAIL_TO=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --json)
            JSON_OUTPUT=true
            shift
            ;;
        --alert)
            ALERT_MODE=true
            shift
            ;;
        --webhook)
            WEBHOOK_URL="$2"
            shift 2
            ;;
        --email)
            EMAIL_TO="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 日志函数
log_info() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

log_success() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "${GREEN}[✅]${NC} $1"
    fi
}

log_warning() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "${YELLOW}[⚠️]${NC} $1"
    fi
}

log_error() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo -e "${RED}[❌]${NC} $1"
    fi
}

# 检查结果存储
declare -A check_results
overall_status="healthy"

# 检查HTTP服务
check_http_service() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log_info "检查 $name..."
    
    if response=$(curl -s -w "%{http_code}" --max-time $TIMEOUT "$url" 2>/dev/null); then
        http_code="${response: -3}"
        if [ "$http_code" = "$expected_status" ]; then
            check_results["$name"]="healthy"
            log_success "$name: 正常 (HTTP $http_code)"
        else
            check_results["$name"]="unhealthy"
            overall_status="unhealthy"
            log_error "$name: HTTP状态码异常 ($http_code)"
        fi
    else
        check_results["$name"]="unreachable"
        overall_status="unhealthy"
        log_error "$name: 无法连接"
    fi
}

# 检查Docker容器
check_docker_container() {
    local name=$1
    local container_name=$2
    
    log_info "检查容器 $name..."
    
    if docker-compose ps -q "$container_name" >/dev/null 2>&1; then
        container_status=$(docker-compose ps "$container_name" | grep "$container_name" | awk '{print $4}')
        if [[ "$container_status" == *"Up"* ]]; then
            check_results["$name"]="healthy"
            log_success "$name: 容器运行正常"
        else
            check_results["$name"]="unhealthy"
            overall_status="unhealthy"
            log_error "$name: 容器状态异常 ($container_status)"
        fi
    else
        check_results["$name"]="not_found"
        overall_status="unhealthy"
        log_error "$name: 容器不存在"
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if docker-compose exec -T postgres pg_isready -U cerberus >/dev/null 2>&1; then
        # 检查数据库查询
        if docker-compose exec -T postgres psql -U cerberus -d cerberus -c "SELECT 1;" >/dev/null 2>&1; then
            check_results["database"]="healthy"
            log_success "数据库: 连接正常"
        else
            check_results["database"]="query_failed"
            overall_status="unhealthy"
            log_error "数据库: 查询失败"
        fi
    else
        check_results["database"]="unreachable"
        overall_status="unhealthy"
        log_error "数据库: 无法连接"
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        check_results["redis"]="healthy"
        log_success "Redis: 连接正常"
    else
        check_results["redis"]="unreachable"
        overall_status="unhealthy"
        log_error "Redis: 无法连接"
    fi
}

# 检查MQTT服务
check_mqtt() {
    log_info "检查MQTT服务..."
    
    if docker-compose exec -T mqtt emqx_ctl status >/dev/null 2>&1; then
        check_results["mqtt"]="healthy"
        log_success "MQTT: 服务正常"
    else
        check_results["mqtt"]="unhealthy"
        overall_status="unhealthy"
        log_error "MQTT: 服务异常"
    fi
}

# 检查MinIO服务
check_minio() {
    log_info "检查MinIO服务..."
    
    if curl -s --max-time $TIMEOUT http://localhost:9000/minio/health/live >/dev/null 2>&1; then
        check_results["minio"]="healthy"
        log_success "MinIO: 服务正常"
    else
        check_results["minio"]="unreachable"
        overall_status="unhealthy"
        log_error "MinIO: 无法连接"
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -lt 80 ]; then
        check_results["disk_space"]="healthy"
        log_success "磁盘空间: 正常 (${disk_usage}%)"
    elif [ "$disk_usage" -lt 90 ]; then
        check_results["disk_space"]="warning"
        log_warning "磁盘空间: 警告 (${disk_usage}%)"
    else
        check_results["disk_space"]="critical"
        overall_status="unhealthy"
        log_error "磁盘空间: 严重不足 (${disk_usage}%)"
    fi
}

# 检查内存使用
check_memory() {
    log_info "检查内存使用..."
    
    memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    if [ "$memory_usage" -lt 80 ]; then
        check_results["memory"]="healthy"
        log_success "内存使用: 正常 (${memory_usage}%)"
    elif [ "$memory_usage" -lt 90 ]; then
        check_results["memory"]="warning"
        log_warning "内存使用: 警告 (${memory_usage}%)"
    else
        check_results["memory"]="critical"
        overall_status="unhealthy"
        log_error "内存使用: 过高 (${memory_usage}%)"
    fi
}

# 发送告警
send_alert() {
    local message="$1"
    
    if [ "$ALERT_MODE" = true ]; then
        # Webhook告警
        if [ -n "$WEBHOOK_URL" ]; then
            curl -X POST "$WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{\"text\":\"$message\"}" >/dev/null 2>&1
        fi
        
        # 邮件告警
        if [ -n "$EMAIL_TO" ] && command -v mail >/dev/null 2>&1; then
            echo "$message" | mail -s "Cerberus Health Alert" "$EMAIL_TO"
        fi
    fi
}

# 生成JSON报告
generate_json_report() {
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo "{"
    echo "  \"timestamp\": \"$timestamp\","
    echo "  \"overall_status\": \"$overall_status\","
    echo "  \"checks\": {"
    
    local first=true
    for service in "${!check_results[@]}"; do
        if [ "$first" = false ]; then
            echo ","
        fi
        echo -n "    \"$service\": \"${check_results[$service]}\""
        first=false
    done
    
    echo ""
    echo "  }"
    echo "}"
}

# 主检查函数
run_health_checks() {
    if [ "$JSON_OUTPUT" = false ]; then
        echo "🔍 Cerberus 系统健康检查"
        echo "=========================="
        echo "时间: $(date)"
        echo
    fi
    
    # 执行各项检查
    check_http_service "中央平台" "http://localhost:8000/health"
    check_http_service "边缘节点" "http://localhost:5000/health"
    check_docker_container "PostgreSQL容器" "postgres"
    check_docker_container "Redis容器" "redis"
    check_docker_container "MQTT容器" "mqtt"
    check_docker_container "MinIO容器" "minio"
    check_database
    check_redis
    check_mqtt
    check_minio
    check_disk_space
    check_memory
    
    # 输出结果
    if [ "$JSON_OUTPUT" = true ]; then
        generate_json_report
    else
        echo
        echo "=========================="
        if [ "$overall_status" = "healthy" ]; then
            log_success "整体状态: 健康"
        else
            log_error "整体状态: 异常"
            
            # 发送告警
            alert_message="Cerberus系统健康检查发现异常，请及时处理。时间: $(date)"
            send_alert "$alert_message"
        fi
        echo "检查完成"
    fi
    
    # 设置退出码
    if [ "$overall_status" = "healthy" ]; then
        exit 0
    else
        exit 1
    fi
}

# 执行健康检查
run_health_checks
