# Cerberus AI检测器依赖包
# 计算机视觉和深度学习
opencv-python==********
opencv-contrib-python==********
numpy==1.24.3
scipy==1.11.1

# 深度学习推理
onnxruntime==1.15.1
onnxruntime-gpu==1.15.1  # GPU版本（可选）

# 人脸识别
face-recognition==1.3.0
dlib==19.24.2

# 图像处理
Pillow==10.0.0
scikit-image==0.21.0

# 数学计算
scikit-learn==1.3.0

# 配置文件处理
PyYAML==6.0.1

# 日志和工具
loguru==0.7.0

# 性能监控
psutil==5.9.5
memory-profiler==0.61.0

# 多线程和异步
threading2==0.1.0

# 数据序列化
pickle5==0.0.12

# 时间处理
python-dateutil==2.8.2

# 数据分析（可选）
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2

# 测试框架（开发用）
pytest==7.4.0
pytest-cov==4.1.0 