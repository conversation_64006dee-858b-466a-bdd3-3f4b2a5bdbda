"""
Cerberus 自动化视频录制模块
实现环形缓冲区支持15秒预录制和15秒事后录制
针对边缘计算设备进行性能优化，支持自动文件管理
"""

import cv2
import numpy as np
import time
import threading
import logging
import os
import shutil
from typing import Dict, List, Optional, Tuple, Any
from collections import deque
from dataclasses import dataclass
from datetime import datetime, timedelta
import queue
import psutil

logger = logging.getLogger(__name__)

@dataclass
class RecordingConfig:
    """录制配置"""
    pre_buffer_seconds: int = 15
    post_buffer_seconds: int = 15
    fps: int = 25
    resolution: Tuple[int, int] = (1280, 720)
    codec: str = 'mp4v'
    quality: int = 80  # 0-100
    max_file_size_mb: int = 100
    output_dir: str = 'cache/videos'
    cleanup_enabled: bool = True
    max_storage_gb: int = 10
    retention_hours: int = 24

@dataclass
class RecordingEvent:
    """录制事件"""
    event_id: str
    camera_id: str
    start_time: float
    trigger_time: float
    end_time: Optional[float] = None
    file_path: Optional[str] = None
    metadata: Dict[str, Any] = None

class RingBuffer:
    """环形缓冲区 - 用于预录制"""
    
    def __init__(self, max_size: int):
        self.max_size = max_size
        self.buffer = deque(maxlen=max_size)
        self.timestamps = deque(maxlen=max_size)
        self.lock = threading.Lock()
    
    def put(self, frame: np.ndarray, timestamp: float):
        """添加帧到缓冲区"""
        with self.lock:
            self.buffer.append(frame.copy())
            self.timestamps.append(timestamp)
    
    def get_frames_since(self, since_time: float) -> List[Tuple[np.ndarray, float]]:
        """获取指定时间之后的所有帧"""
        with self.lock:
            frames = []
            for i, timestamp in enumerate(self.timestamps):
                if timestamp >= since_time:
                    frames.append((self.buffer[i].copy(), timestamp))
            return frames
    
    def get_all_frames(self) -> List[Tuple[np.ndarray, float]]:
        """获取所有帧"""
        with self.lock:
            return [(frame.copy(), ts) for frame, ts in zip(self.buffer, self.timestamps)]
    
    def clear(self):
        """清空缓冲区"""
        with self.lock:
            self.buffer.clear()
            self.timestamps.clear()
    
    def size(self) -> int:
        """获取当前大小"""
        return len(self.buffer)

class VideoWriter:
    """视频写入器"""
    
    def __init__(self, file_path: str, config: RecordingConfig):
        self.file_path = file_path
        self.config = config
        self.writer = None
        self.frame_count = 0
        self.start_time = time.time()
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 初始化视频写入器
        self._initialize_writer()
    
    def _initialize_writer(self):
        """初始化OpenCV视频写入器"""
        try:
            # 获取编码器
            fourcc = cv2.VideoWriter_fourcc(*self.config.codec)
            
            # 创建视频写入器
            self.writer = cv2.VideoWriter(
                self.file_path,
                fourcc,
                self.config.fps,
                self.config.resolution
            )
            
            if not self.writer.isOpened():
                raise Exception("无法创建视频写入器")
            
            logger.debug(f"视频写入器初始化成功: {self.file_path}")
            
        except Exception as e:
            logger.error(f"视频写入器初始化失败: {e}")
            raise
    
    def write_frame(self, frame: np.ndarray) -> bool:
        """写入帧"""
        try:
            if self.writer is None:
                return False
            
            # 调整帧大小
            if frame.shape[:2][::-1] != self.config.resolution:
                frame = cv2.resize(frame, self.config.resolution)
            
            self.writer.write(frame)
            self.frame_count += 1
            return True
            
        except Exception as e:
            logger.error(f"写入帧失败: {e}")
            return False
    
    def finalize(self) -> bool:
        """完成录制"""
        try:
            if self.writer:
                self.writer.release()
                self.writer = None
            
            # 检查文件是否成功创建
            if os.path.exists(self.file_path) and os.path.getsize(self.file_path) > 0:
                duration = time.time() - self.start_time
                logger.info(f"视频录制完成: {self.file_path}, 帧数: {self.frame_count}, 时长: {duration:.1f}s")
                return True
            else:
                logger.error(f"视频文件创建失败: {self.file_path}")
                return False
                
        except Exception as e:
            logger.error(f"完成录制失败: {e}")
            return False

class VideoRecorder:
    """视频录制器主类"""
    
    def __init__(self, camera_id: str, config: RecordingConfig):
        self.camera_id = camera_id
        self.config = config
        
        # 环形缓冲区 - 用于预录制
        buffer_size = config.pre_buffer_seconds * config.fps
        self.ring_buffer = RingBuffer(buffer_size)
        
        # 录制状态
        self.is_recording = False
        self.current_recording: Optional[RecordingEvent] = None
        self.recording_writer: Optional[VideoWriter] = None
        
        # 后录制定时器
        self.post_record_timer: Optional[threading.Timer] = None
        
        # 线程安全
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_recordings': 0,
            'total_frames_recorded': 0,
            'total_size_mb': 0,
            'last_recording_time': 0
        }
        
        logger.info(f"视频录制器初始化完成 - 摄像头: {camera_id}")
    
    def add_frame(self, frame: np.ndarray):
        """添加帧到环形缓冲区"""
        if frame is None or frame.size == 0:
            return
        
        timestamp = time.time()
        
        # 添加到环形缓冲区
        self.ring_buffer.put(frame, timestamp)
        
        # 如果正在录制，写入当前帧
        if self.is_recording and self.recording_writer:
            self.recording_writer.write_frame(frame)
    
    def start_recording(self, event_id: str, trigger_time: Optional[float] = None) -> bool:
        """开始录制"""
        with self.lock:
            if self.is_recording:
                logger.warning(f"摄像头 {self.camera_id} 已在录制中")
                return False
            
            try:
                if trigger_time is None:
                    trigger_time = time.time()
                
                # 创建录制事件
                self.current_recording = RecordingEvent(
                    event_id=event_id,
                    camera_id=self.camera_id,
                    start_time=trigger_time - self.config.pre_buffer_seconds,
                    trigger_time=trigger_time,
                    metadata={}
                )
                
                # 生成文件路径
                timestamp_str = datetime.fromtimestamp(trigger_time).strftime('%Y%m%d_%H%M%S')
                filename = f"{self.camera_id}_{event_id}_{timestamp_str}.mp4"
                file_path = os.path.join(self.config.output_dir, filename)
                self.current_recording.file_path = file_path
                
                # 创建视频写入器
                self.recording_writer = VideoWriter(file_path, self.config)
                
                # 写入预录制的帧
                pre_record_start = trigger_time - self.config.pre_buffer_seconds
                pre_frames = self.ring_buffer.get_frames_since(pre_record_start)
                
                for frame, timestamp in pre_frames:
                    self.recording_writer.write_frame(frame)
                
                self.is_recording = True
                
                # 设置后录制定时器
                self._schedule_stop_recording()
                
                logger.info(f"开始录制 - 摄像头: {self.camera_id}, 事件: {event_id}, 预录制帧数: {len(pre_frames)}")
                return True
                
            except Exception as e:
                logger.error(f"开始录制失败: {e}")
                self._cleanup_recording()
                return False
    
    def _schedule_stop_recording(self):
        """安排停止录制"""
        if self.post_record_timer:
            self.post_record_timer.cancel()
        
        self.post_record_timer = threading.Timer(
            self.config.post_buffer_seconds,
            self._stop_recording
        )
        self.post_record_timer.start()
    
    def _stop_recording(self):
        """停止录制"""
        with self.lock:
            if not self.is_recording:
                return
            
            try:
                # 完成录制
                if self.recording_writer:
                    success = self.recording_writer.finalize()
                    
                    if success and self.current_recording:
                        self.current_recording.end_time = time.time()
                        
                        # 更新统计信息
                        self.stats['total_recordings'] += 1
                        self.stats['last_recording_time'] = time.time()
                        
                        if os.path.exists(self.current_recording.file_path):
                            file_size = os.path.getsize(self.current_recording.file_path)
                            self.stats['total_size_mb'] += file_size / (1024 * 1024)
                        
                        logger.info(f"录制完成 - 文件: {self.current_recording.file_path}")
                    
                self._cleanup_recording()
                
            except Exception as e:
                logger.error(f"停止录制失败: {e}")
                self._cleanup_recording()
    
    def _cleanup_recording(self):
        """清理录制资源"""
        self.is_recording = False
        self.current_recording = None
        
        if self.recording_writer:
            self.recording_writer.finalize()
            self.recording_writer = None
        
        if self.post_record_timer:
            self.post_record_timer.cancel()
            self.post_record_timer = None
    
    def extend_recording(self, additional_seconds: int = 15):
        """延长录制时间"""
        if self.is_recording and self.post_record_timer:
            self.post_record_timer.cancel()
            
            self.post_record_timer = threading.Timer(
                additional_seconds,
                self._stop_recording
            )
            self.post_record_timer.start()
            
            logger.info(f"录制时间延长 {additional_seconds} 秒")
    
    def force_stop_recording(self):
        """强制停止录制"""
        with self.lock:
            if self.is_recording:
                self._stop_recording()
                logger.info(f"强制停止录制 - 摄像头: {self.camera_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取录制统计信息"""
        return {
            'camera_id': self.camera_id,
            'is_recording': self.is_recording,
            'buffer_size': self.ring_buffer.size(),
            'total_recordings': self.stats['total_recordings'],
            'total_size_mb': round(self.stats['total_size_mb'], 2),
            'last_recording_time': self.stats['last_recording_time'],
            'current_recording': {
                'event_id': self.current_recording.event_id if self.current_recording else None,
                'start_time': self.current_recording.start_time if self.current_recording else None,
                'file_path': self.current_recording.file_path if self.current_recording else None
            } if self.is_recording else None
        }
    
    def cleanup(self):
        """清理资源"""
        self.force_stop_recording()
        self.ring_buffer.clear()
        logger.info(f"视频录制器清理完成 - 摄像头: {self.camera_id}")

class VideoFileManager:
    """视频文件管理器 - 负责文件清理和存储管理"""

    def __init__(self, config: RecordingConfig):
        self.config = config
        self.cleanup_thread = None
        self.running = False

        # 确保输出目录存在
        os.makedirs(config.output_dir, exist_ok=True)

        if config.cleanup_enabled:
            self.start_cleanup_service()

    def start_cleanup_service(self):
        """启动清理服务"""
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            return

        self.running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info("视频文件清理服务已启动")

    def stop_cleanup_service(self):
        """停止清理服务"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        logger.info("视频文件清理服务已停止")

    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                self.cleanup_old_files()
                self.cleanup_by_storage_limit()

                # 每小时检查一次
                time.sleep(3600)

            except Exception as e:
                logger.error(f"文件清理失败: {e}")
                time.sleep(300)  # 出错时5分钟后重试

    def cleanup_old_files(self):
        """清理过期文件"""
        try:
            current_time = time.time()
            retention_seconds = self.config.retention_hours * 3600
            deleted_count = 0
            deleted_size = 0

            for root, dirs, files in os.walk(self.config.output_dir):
                for file in files:
                    if file.endswith('.mp4'):
                        file_path = os.path.join(root, file)

                        # 检查文件修改时间
                        file_mtime = os.path.getmtime(file_path)
                        if current_time - file_mtime > retention_seconds:
                            try:
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                deleted_count += 1
                                deleted_size += file_size
                                logger.debug(f"删除过期文件: {file_path}")
                            except Exception as e:
                                logger.error(f"删除文件失败 {file_path}: {e}")

            if deleted_count > 0:
                deleted_size_mb = deleted_size / (1024 * 1024)
                logger.info(f"清理过期文件完成: 删除 {deleted_count} 个文件, 释放 {deleted_size_mb:.1f}MB")

        except Exception as e:
            logger.error(f"清理过期文件失败: {e}")

    def cleanup_by_storage_limit(self):
        """按存储限制清理文件"""
        try:
            # 获取目录大小
            total_size = self.get_directory_size(self.config.output_dir)
            max_size = self.config.max_storage_gb * 1024 * 1024 * 1024  # 转换为字节

            if total_size <= max_size:
                return

            logger.info(f"存储空间超限: {total_size / (1024**3):.1f}GB / {self.config.max_storage_gb}GB")

            # 获取所有视频文件并按修改时间排序
            video_files = []
            for root, dirs, files in os.walk(self.config.output_dir):
                for file in files:
                    if file.endswith('.mp4'):
                        file_path = os.path.join(root, file)
                        mtime = os.path.getmtime(file_path)
                        size = os.path.getsize(file_path)
                        video_files.append((file_path, mtime, size))

            # 按修改时间排序（最旧的在前）
            video_files.sort(key=lambda x: x[1])

            # 删除最旧的文件直到满足存储限制
            deleted_count = 0
            deleted_size = 0
            target_size = max_size * 0.8  # 删除到80%容量

            for file_path, mtime, size in video_files:
                if total_size - deleted_size <= target_size:
                    break

                try:
                    os.remove(file_path)
                    deleted_count += 1
                    deleted_size += size
                    logger.debug(f"删除文件以释放空间: {file_path}")
                except Exception as e:
                    logger.error(f"删除文件失败 {file_path}: {e}")

            if deleted_count > 0:
                deleted_size_mb = deleted_size / (1024 * 1024)
                logger.info(f"存储清理完成: 删除 {deleted_count} 个文件, 释放 {deleted_size_mb:.1f}MB")

        except Exception as e:
            logger.error(f"存储清理失败: {e}")

    def get_directory_size(self, directory: str) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
        except Exception as e:
            logger.error(f"计算目录大小失败: {e}")

        return total_size

    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        try:
            # 目录大小
            used_size = self.get_directory_size(self.config.output_dir)
            max_size = self.config.max_storage_gb * 1024 * 1024 * 1024

            # 文件数量
            file_count = 0
            for root, dirs, files in os.walk(self.config.output_dir):
                file_count += len([f for f in files if f.endswith('.mp4')])

            # 磁盘空间
            disk_usage = psutil.disk_usage(self.config.output_dir)

            return {
                'used_size_gb': round(used_size / (1024**3), 2),
                'max_size_gb': self.config.max_storage_gb,
                'usage_percent': round((used_size / max_size) * 100, 1) if max_size > 0 else 0,
                'file_count': file_count,
                'disk_total_gb': round(disk_usage.total / (1024**3), 2),
                'disk_used_gb': round(disk_usage.used / (1024**3), 2),
                'disk_free_gb': round(disk_usage.free / (1024**3), 2),
                'disk_usage_percent': round((disk_usage.used / disk_usage.total) * 100, 1)
            }

        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {}

    def force_cleanup(self, target_size_gb: Optional[float] = None):
        """强制清理到指定大小"""
        if target_size_gb is None:
            target_size_gb = self.config.max_storage_gb * 0.5  # 清理到50%

        logger.info(f"开始强制清理，目标大小: {target_size_gb}GB")

        # 临时修改配置
        original_max_size = self.config.max_storage_gb
        self.config.max_storage_gb = target_size_gb

        try:
            self.cleanup_by_storage_limit()
        finally:
            # 恢复原配置
            self.config.max_storage_gb = original_max_size

class RecordingManager:
    """录制管理器 - 管理多个摄像头的录制"""

    def __init__(self, config: RecordingConfig):
        self.config = config
        self.recorders: Dict[str, VideoRecorder] = {}
        self.file_manager = VideoFileManager(config)
        self.lock = threading.Lock()

        logger.info("录制管理器初始化完成")

    def get_or_create_recorder(self, camera_id: str) -> VideoRecorder:
        """获取或创建录制器"""
        with self.lock:
            if camera_id not in self.recorders:
                self.recorders[camera_id] = VideoRecorder(camera_id, self.config)
            return self.recorders[camera_id]

    def add_frame(self, camera_id: str, frame: np.ndarray):
        """添加帧"""
        recorder = self.get_or_create_recorder(camera_id)
        recorder.add_frame(frame)

    def start_recording(self, camera_id: str, event_id: str) -> bool:
        """开始录制"""
        recorder = self.get_or_create_recorder(camera_id)
        return recorder.start_recording(event_id)

    def stop_recording(self, camera_id: str):
        """停止录制"""
        if camera_id in self.recorders:
            self.recorders[camera_id].force_stop_recording()

    def extend_recording(self, camera_id: str, additional_seconds: int = 15):
        """延长录制"""
        if camera_id in self.recorders:
            self.recorders[camera_id].extend_recording(additional_seconds)

    def get_recording_stats(self) -> Dict[str, Any]:
        """获取录制统计"""
        stats = {
            'total_recorders': len(self.recorders),
            'active_recordings': 0,
            'storage_info': self.file_manager.get_storage_info(),
            'recorders': {}
        }

        for camera_id, recorder in self.recorders.items():
            recorder_stats = recorder.get_stats()
            stats['recorders'][camera_id] = recorder_stats
            if recorder_stats['is_recording']:
                stats['active_recordings'] += 1

        return stats

    def cleanup(self):
        """清理所有资源"""
        with self.lock:
            for recorder in self.recorders.values():
                recorder.cleanup()
            self.recorders.clear()

        self.file_manager.stop_cleanup_service()
        logger.info("录制管理器清理完成")
