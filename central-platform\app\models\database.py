"""
数据库模型定义
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Text, Float,
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()

class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class User(Base, TimestampMixin):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    last_login = Column(DateTime)
    
    # 关系
    roles = relationship("UserRole", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")

class Role(Base, TimestampMixin):
    """角色表"""
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    is_active = Column(Boolean, default=True)
    
    # 关系
    users = relationship("UserRole", back_populates="role")
    permissions = relationship("RolePermission", back_populates="role")

class Permission(Base, TimestampMixin):
    """权限表"""
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    resource = Column(String(50), nullable=False)  # 资源类型
    action = Column(String(50), nullable=False)    # 操作类型
    
    # 关系
    roles = relationship("RolePermission", back_populates="permission")

class UserRole(Base, TimestampMixin):
    """用户角色关联表"""
    __tablename__ = "user_roles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False)
    
    # 关系
    user = relationship("User", back_populates="roles")
    role = relationship("Role", back_populates="users")
    
    __table_args__ = (UniqueConstraint("user_id", "role_id"),)

class RolePermission(Base, TimestampMixin):
    """角色权限关联表"""
    __tablename__ = "role_permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False)
    permission_id = Column(Integer, ForeignKey("permissions.id"), nullable=False)
    
    # 关系
    role = relationship("Role", back_populates="permissions")
    permission = relationship("Permission", back_populates="roles")
    
    __table_args__ = (UniqueConstraint("role_id", "permission_id"),)

class Node(Base, TimestampMixin):
    """边缘节点表"""
    __tablename__ = "nodes"
    
    id = Column(Integer, primary_key=True, index=True)
    node_id = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String(500))
    location = Column(String(200))
    ip_address = Column(String(45))  # 支持IPv6
    status = Column(String(20), default="offline")  # online, offline, error
    last_heartbeat = Column(DateTime)
    version = Column(String(20))
    config = Column(JSON)
    node_metadata = Column(JSON)
    
    # 关系
    cameras = relationship("Camera", back_populates="node")
    events = relationship("Event", back_populates="node")
    
    # 索引
    __table_args__ = (
        Index("idx_node_status", "status"),
        Index("idx_node_last_heartbeat", "last_heartbeat"),
    )

class Camera(Base, TimestampMixin):
    """摄像头表"""
    __tablename__ = "cameras"
    
    id = Column(Integer, primary_key=True, index=True)
    camera_id = Column(String(100), index=True, nullable=False)
    node_id = Column(Integer, ForeignKey("nodes.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String(500))
    rtsp_url = Column(String(500))
    location = Column(String(200))
    status = Column(String(20), default="inactive")  # active, inactive, error
    resolution = Column(String(20))  # 1920x1080
    fps = Column(Integer)
    config = Column(JSON)
    camera_metadata = Column(JSON)
    
    # 关系
    node = relationship("Node", back_populates="cameras")
    events = relationship("Event", back_populates="camera")
    
    # 索引
    __table_args__ = (
        Index("idx_camera_node_id", "node_id"),
        Index("idx_camera_status", "status"),
        UniqueConstraint("node_id", "camera_id"),
    )

class Event(Base, TimestampMixin):
    """事件表"""
    __tablename__ = "events"
    
    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(String(100), unique=True, index=True, nullable=False)
    node_id = Column(Integer, ForeignKey("nodes.id"), nullable=False)
    camera_id = Column(Integer, ForeignKey("cameras.id"), nullable=False)
    event_type = Column(String(50), nullable=False)
    confidence = Column(Float)
    bbox = Column(JSON)  # 边界框坐标
    description = Column(String(1000))
    status = Column(String(20), default="new")  # new, reviewed, archived
    severity = Column(String(20), default="medium")  # low, medium, high, critical
    event_metadata = Column(JSON)
    
    # 关系
    node = relationship("Node", back_populates="events")
    camera = relationship("Camera", back_populates="events")
    videos = relationship("EventVideo", back_populates="event")
    
    # 索引
    __table_args__ = (
        Index("idx_event_type", "event_type"),
        Index("idx_event_status", "status"),
        Index("idx_event_severity", "severity"),
        Index("idx_event_created_at", "created_at"),
        Index("idx_event_node_camera", "node_id", "camera_id"),
    )

class EventVideo(Base, TimestampMixin):
    """事件视频表"""
    __tablename__ = "event_videos"
    
    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(Integer, ForeignKey("events.id"), nullable=False)
    video_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # 字节
    duration = Column(Float)     # 秒
    format = Column(String(10))  # mp4, avi等
    resolution = Column(String(20))
    fps = Column(Integer)
    thumbnail_path = Column(String(500))
    video_metadata = Column(JSON)
    
    # 关系
    event = relationship("Event", back_populates="videos")
    
    # 索引
    __table_args__ = (
        Index("idx_video_event_id", "event_id"),
    )

class SystemLog(Base, TimestampMixin):
    """系统日志表"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    module = Column(String(100))
    function = Column(String(100))
    line_number = Column(Integer)
    exception = Column(Text)
    log_metadata = Column(JSON)
    
    # 索引
    __table_args__ = (
        Index("idx_log_level", "level"),
        Index("idx_log_created_at", "created_at"),
        Index("idx_log_module", "module"),
    )

class AuditLog(Base, TimestampMixin):
    """审计日志表"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50))
    resource_id = Column(String(100))
    ip_address = Column(String(45))
    user_agent = Column(String(500))
    details = Column(JSON)
    
    # 关系
    user = relationship("User", back_populates="audit_logs")
    
    # 索引
    __table_args__ = (
        Index("idx_audit_user_id", "user_id"),
        Index("idx_audit_action", "action"),
        Index("idx_audit_resource", "resource_type", "resource_id"),
        Index("idx_audit_created_at", "created_at"),
    )

class Setting(Base, TimestampMixin):
    """系统设置表"""
    __tablename__ = "settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text)
    description = Column(String(500))
    category = Column(String(50))
    data_type = Column(String(20), default="string")  # string, integer, float, boolean, json
    is_public = Column(Boolean, default=False)  # 是否可以公开访问
    
    # 索引
    __table_args__ = (
        Index("idx_setting_category", "category"),
    )

class Notification(Base, TimestampMixin):
    """通知表"""
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    type = Column(String(50), default="info")  # info, warning, error, success
    target_type = Column(String(50))  # user, role, all
    target_id = Column(String(100))
    is_read = Column(Boolean, default=False)
    expires_at = Column(DateTime)
    notification_metadata = Column(JSON)
    
    # 索引
    __table_args__ = (
        Index("idx_notification_target", "target_type", "target_id"),
        Index("idx_notification_type", "type"),
        Index("idx_notification_is_read", "is_read"),
        Index("idx_notification_created_at", "created_at"),
    )
