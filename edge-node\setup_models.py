#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型下载和设置脚本
自动下载和配置Cerberus智能安防平台所需的AI模型
"""

import os
import urllib.request
import bz2
import shutil
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_directories():
    """创建模型目录结构"""
    directories = [
        "models/detection",
        "models/face", 
        "models/face/known_faces",
        "models/camera",
        "models/camera/reference_frames"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {directory}")

def download_file(url, filepath, description=""):
    """下载文件"""
    try:
        logger.info(f"开始下载 {description}: {url}")
        urllib.request.urlretrieve(url, filepath)
        logger.info(f"下载完成: {filepath}")
        return True
    except Exception as e:
        logger.error(f"下载失败 {description}: {e}")
        return False

def extract_bz2(filepath):
    """解压bz2文件"""
    try:
        extracted_path = filepath.replace('.bz2', '')
        with bz2.BZ2File(filepath, 'rb') as f_in:
            with open(extracted_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.remove(filepath)  # 删除压缩文件
        logger.info(f"解压完成: {extracted_path}")
        return True
    except Exception as e:
        logger.error(f"解压失败: {e}")
        return False

def download_yolo_models():
    """下载YOLO模型"""
    models = [
        {
            "name": "YOLOv8 Nano",
            "url": "https://github.com/ultralytics/assets/releases/download/v8.0.0/yolov8n.pt",
            "path": "models/detection/yolov8n.pt"
        },
        {
            "name": "YOLOv8 Small", 
            "url": "https://github.com/ultralytics/assets/releases/download/v8.0.0/yolov8s.pt",
            "path": "models/detection/yolov8s.pt"
        }
    ]
    
    for model in models:
        if not os.path.exists(model["path"]):
            download_file(model["url"], model["path"], model["name"])
        else:
            logger.info(f"模型已存在: {model['path']}")

def download_dlib_models():
    """下载dlib人脸模型"""
    model_url = "http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2"
    bz2_path = "models/face/shape_predictor_68_face_landmarks.dat.bz2"
    final_path = "models/face/shape_predictor_68_face_landmarks.dat"
    
    if not os.path.exists(final_path):
        if download_file(model_url, bz2_path, "dlib人脸标志模型"):
            extract_bz2(bz2_path)
    else:
        logger.info(f"模型已存在: {final_path}")

def create_sample_config():
    """创建示例人脸数据库配置"""
    config_content = """# 人脸数据库配置示例
# 
# 使用方法:
# 1. 将人员照片放置在 models/face/known_faces/ 目录下
# 2. 照片命名格式: 姓名.jpg (如: 张三.jpg)
# 3. 确保照片清晰，人脸正面，光线充足
# 4. 每个人可以有多张照片，使用 姓名_1.jpg, 姓名_2.jpg 格式
#
# 支持的图片格式: .jpg, .jpeg, .png
# 推荐图片尺寸: 至少300x300像素
"""
    
    config_path = "models/face/README.txt"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    logger.info(f"创建配置文件: {config_path}")

def check_dependencies():
    """检查Python依赖"""
    required_packages = [
        "ultralytics",
        "opencv-python", 
        "face_recognition",
        "dlib",
        "numpy",
        "Pillow"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.warning(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements_ai.txt")
    else:
        logger.info("所有依赖包已安装")

def main():
    """主函数"""
    logger.info("=== Cerberus 智能安防平台模型设置 ===")
    
    # 创建目录结构
    create_directories()
    
    # 检查依赖
    check_dependencies()
    
    # 下载模型
    logger.info("开始下载AI模型...")
    download_yolo_models()
    download_dlib_models()
    
    # 创建配置文件
    create_sample_config()
    
    logger.info("=== 模型设置完成 ===")
    logger.info("下一步:")
    logger.info("1. 检查 models/ 目录下的模型文件")
    logger.info("2. 将人员照片添加到 models/face/known_faces/ 目录")
    logger.info("3. 运行测试脚本: python test_ai_detectors.py")

if __name__ == "__main__":
    main() 