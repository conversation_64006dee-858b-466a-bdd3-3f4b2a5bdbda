"""
统计分析端点
"""

from fastapi import APIRouter, HTTPException, Query
from app.services.analytics_service import AnalyticsService

router = APIRouter()

@router.get("/overview")
async def get_system_overview():
    """获取系统概览"""
    try:
        analytics_service = AnalyticsService()
        overview = await analytics_service.get_system_overview()
        return overview
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system overview: {str(e)}")

@router.get("/daily-report")
async def get_daily_report():
    """获取日报"""
    try:
        analytics_service = AnalyticsService()
        report = await analytics_service.generate_daily_report()
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating daily report: {str(e)}")

@router.get("/trends")
async def get_trend_analysis(days: int = Query(7, ge=1, le=365)):
    """获取趋势分析"""
    try:
        analytics_service = AnalyticsService()
        trends = await analytics_service.get_trend_analysis(days)
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting trend analysis: {str(e)}")

@router.get("/node-performance")
async def get_node_performance():
    """获取节点性能分析"""
    try:
        analytics_service = AnalyticsService()
        performance = await analytics_service.get_node_performance()
        return performance
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting node performance: {str(e)}")

@router.get("/event-types")
async def get_event_type_analysis(days: int = Query(30, ge=1, le=365)):
    """获取事件类型分析"""
    try:
        analytics_service = AnalyticsService()
        analysis = await analytics_service.get_event_type_analysis(days)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting event type analysis: {str(e)}")
