"""
事件管理端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc

from app.core.database import get_db
from app.models.database import Event, Node, Camera

router = APIRouter()

@router.get("/")
async def list_events(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    event_type: Optional[str] = None,
    status: Optional[str] = None,
    severity: Optional[str] = None,
    node_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取事件列表"""
    try:
        query = select(Event).order_by(desc(Event.created_at))
        
        # 添加过滤条件
        if event_type:
            query = query.where(Event.event_type == event_type)
        if status:
            query = query.where(Event.status == status)
        if severity:
            query = query.where(Event.severity == severity)
        if node_id:
            # 通过节点ID过滤
            node_result = await db.execute(select(Node.id).where(Node.node_id == node_id))
            node_db_id = node_result.scalar_one_or_none()
            if node_db_id:
                query = query.where(Event.node_id == node_db_id)
            else:
                return []  # 节点不存在，返回空列表
        
        # 分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        events = result.scalars().all()
        
        return [
            {
                "id": event.id,
                "event_id": event.event_id,
                "event_type": event.event_type,
                "confidence": event.confidence,
                "description": event.description,
                "status": event.status,
                "severity": event.severity,
                "bbox": event.bbox,
                "metadata": event.metadata,
                "created_at": event.created_at.isoformat(),
                "updated_at": event.updated_at.isoformat()
            }
            for event in events
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching events: {str(e)}")

@router.get("/{event_id}")
async def get_event(event_id: str, db: AsyncSession = Depends(get_db)):
    """获取特定事件信息"""
    try:
        result = await db.execute(select(Event).where(Event.event_id == event_id))
        event = result.scalar_one_or_none()
        
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        return {
            "id": event.id,
            "event_id": event.event_id,
            "event_type": event.event_type,
            "confidence": event.confidence,
            "description": event.description,
            "status": event.status,
            "severity": event.severity,
            "bbox": event.bbox,
            "metadata": event.metadata,
            "created_at": event.created_at.isoformat(),
            "updated_at": event.updated_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching event: {str(e)}")

@router.get("/stats/summary")
async def get_events_summary(db: AsyncSession = Depends(get_db)):
    """获取事件统计摘要"""
    try:
        # 这里可以添加更复杂的统计查询
        total_result = await db.execute(select(Event.id).count())
        total_events = total_result.scalar()
        
        return {
            "total_events": total_events,
            "by_status": {},
            "by_severity": {},
            "by_type": {}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching events summary: {str(e)}")
