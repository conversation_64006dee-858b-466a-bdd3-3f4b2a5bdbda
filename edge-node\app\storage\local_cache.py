"""
Cerberus 本地缓存模块
提供离线数据缓存和同步功能
"""

import os
import json
import time
import sqlite3
import logging
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import shutil

logger = logging.getLogger(__name__)

class LocalCache:
    """本地缓存管理器"""
    
    def __init__(self, config):
        self.config = config
        self.cache_dir = config.CACHE_DIR
        self.db_path = os.path.join(self.cache_dir, 'cache.db')
        self.max_size_gb = config.CACHE_MAX_SIZE_GB
        self.cleanup_interval = config.CACHE_CLEANUP_INTERVAL
        
        # 数据库连接
        self.db_lock = threading.Lock()
        
        # 清理线程
        self.cleanup_thread = None
        self.running = False
        
        # 统计信息
        self.stats = {
            'events_cached': 0,
            'events_synced': 0,
            'cache_size_mb': 0,
            'last_cleanup_time': 0,
            'last_sync_time': 0
        }
        
        logger.info("本地缓存管理器初始化完成")
    
    def initialize(self):
        """初始化缓存"""
        try:
            # 创建缓存目录
            os.makedirs(self.cache_dir, exist_ok=True)
            os.makedirs(os.path.join(self.cache_dir, 'events'), exist_ok=True)
            os.makedirs(os.path.join(self.cache_dir, 'videos'), exist_ok=True)
            
            # 初始化数据库
            self._initialize_database()
            
            # 启动清理服务
            self._start_cleanup_service()
            
            # 更新统计信息
            self._update_stats()
            
            logger.info("本地缓存初始化完成")
            
        except Exception as e:
            logger.error(f"本地缓存初始化失败: {e}")
            raise
    
    def _initialize_database(self):
        """初始化SQLite数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建事件表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        event_id TEXT UNIQUE NOT NULL,
                        node_id TEXT NOT NULL,
                        camera_id TEXT NOT NULL,
                        event_type TEXT NOT NULL,
                        confidence REAL,
                        bbox TEXT,
                        timestamp REAL NOT NULL,
                        video_path TEXT,
                        metadata TEXT,
                        synced INTEGER DEFAULT 0,
                        created_at REAL DEFAULT (strftime('%s', 'now'))
                    )
                ''')
                
                # 创建配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS config_cache (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        updated_at REAL DEFAULT (strftime('%s', 'now'))
                    )
                ''')
                
                # 创建同步状态表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sync_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sync_type TEXT NOT NULL,
                        last_sync_time REAL,
                        status TEXT,
                        error_message TEXT
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_synced ON events(synced)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_node_camera ON events(node_id, camera_id)')
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def cache_event(self, event_data: Dict[str, Any]) -> bool:
        """缓存事件数据"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 插入事件数据
                    cursor.execute('''
                        INSERT OR REPLACE INTO events 
                        (event_id, node_id, camera_id, event_type, confidence, bbox, 
                         timestamp, video_path, metadata, synced)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
                    ''', (
                        event_data.get('event_id'),
                        event_data.get('node_id'),
                        event_data.get('camera_id'),
                        event_data.get('event_type'),
                        event_data.get('confidence'),
                        json.dumps(event_data.get('bbox')) if event_data.get('bbox') else None,
                        event_data.get('timestamp', time.time()),
                        event_data.get('video_path'),
                        json.dumps(event_data.get('metadata', {})),
                    ))
                    
                    conn.commit()
                    self.stats['events_cached'] += 1
                    
                    logger.debug(f"事件已缓存: {event_data.get('event_id')}")
                    return True
                    
        except Exception as e:
            logger.error(f"缓存事件失败: {e}")
            return False
    
    def get_pending_events(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取待同步的事件"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT event_id, node_id, camera_id, event_type, confidence, 
                               bbox, timestamp, video_path, metadata
                        FROM events 
                        WHERE synced = 0 
                        ORDER BY timestamp ASC 
                        LIMIT ?
                    ''', (limit,))
                    
                    events = []
                    for row in cursor.fetchall():
                        event = {
                            'event_id': row[0],
                            'node_id': row[1],
                            'camera_id': row[2],
                            'event_type': row[3],
                            'confidence': row[4],
                            'bbox': json.loads(row[5]) if row[5] else None,
                            'timestamp': row[6],
                            'video_path': row[7],
                            'metadata': json.loads(row[8]) if row[8] else {}
                        }
                        events.append(event)
                    
                    return events
                    
        except Exception as e:
            logger.error(f"获取待同步事件失败: {e}")
            return []
    
    def mark_event_synced(self, event_id: str) -> bool:
        """标记事件为已同步"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        UPDATE events SET synced = 1 WHERE event_id = ?
                    ''', (event_id,))
                    
                    conn.commit()
                    
                    if cursor.rowcount > 0:
                        self.stats['events_synced'] += 1
                        logger.debug(f"事件标记为已同步: {event_id}")
                        return True
                    else:
                        logger.warning(f"未找到事件: {event_id}")
                        return False
                        
        except Exception as e:
            logger.error(f"标记事件同步状态失败: {e}")
            return False
    
    def sync_pending_events(self) -> int:
        """同步待处理事件"""
        try:
            from ..mqtt.publisher import MQTTPublisher
            
            # 获取待同步事件
            pending_events = self.get_pending_events(50)
            
            if not pending_events:
                return 0
            
            synced_count = 0
            
            # 这里应该实现实际的同步逻辑
            # 简化实现：假设所有事件都同步成功
            for event in pending_events:
                # 实际应该发送到中央平台或MQTT
                if self.mark_event_synced(event['event_id']):
                    synced_count += 1
            
            self.stats['last_sync_time'] = time.time()
            logger.info(f"同步完成，成功同步 {synced_count} 个事件")
            
            return synced_count
            
        except Exception as e:
            logger.error(f"同步事件失败: {e}")
            return 0
    
    def cache_config(self, key: str, value: Any) -> bool:
        """缓存配置"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO config_cache (key, value, updated_at)
                        VALUES (?, ?, ?)
                    ''', (key, json.dumps(value), time.time()))
                    
                    conn.commit()
                    logger.debug(f"配置已缓存: {key}")
                    return True
                    
        except Exception as e:
            logger.error(f"缓存配置失败: {e}")
            return False
    
    def get_cached_config(self, key: str, default: Any = None) -> Any:
        """获取缓存的配置"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT value FROM config_cache WHERE key = ?
                    ''', (key,))
                    
                    row = cursor.fetchone()
                    if row:
                        return json.loads(row[0])
                    else:
                        return default
                        
        except Exception as e:
            logger.error(f"获取缓存配置失败: {e}")
            return default
    
    def _start_cleanup_service(self):
        """启动清理服务"""
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            return
        
        self.running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info("缓存清理服务已启动")
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                self._cleanup_old_data()
                self._cleanup_by_size()
                self._update_stats()
                
                self.stats['last_cleanup_time'] = time.time()
                
                # 等待清理间隔
                time.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"缓存清理失败: {e}")
                time.sleep(300)  # 出错时5分钟后重试
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            # 清理7天前的已同步事件
            cutoff_time = time.time() - (7 * 24 * 3600)
            
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 删除过期的已同步事件
                    cursor.execute('''
                        DELETE FROM events 
                        WHERE synced = 1 AND timestamp < ?
                    ''', (cutoff_time,))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
                    if deleted_count > 0:
                        logger.info(f"清理过期事件: {deleted_count} 个")
                        
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    def _cleanup_by_size(self):
        """按大小清理缓存"""
        try:
            current_size = self._get_cache_size()
            max_size = self.max_size_gb * 1024 * 1024 * 1024  # 转换为字节
            
            if current_size <= max_size:
                return
            
            logger.info(f"缓存大小超限: {current_size / (1024**3):.1f}GB / {self.max_size_gb}GB")
            
            # 删除最旧的已同步事件
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 获取最旧的已同步事件
                    cursor.execute('''
                        SELECT event_id, video_path FROM events 
                        WHERE synced = 1 
                        ORDER BY timestamp ASC 
                        LIMIT 100
                    ''')
                    
                    events_to_delete = cursor.fetchall()
                    
                    for event_id, video_path in events_to_delete:
                        # 删除视频文件
                        if video_path and os.path.exists(video_path):
                            try:
                                os.remove(video_path)
                            except Exception as e:
                                logger.error(f"删除视频文件失败 {video_path}: {e}")
                        
                        # 删除数据库记录
                        cursor.execute('DELETE FROM events WHERE event_id = ?', (event_id,))
                    
                    conn.commit()
                    logger.info(f"按大小清理缓存: 删除 {len(events_to_delete)} 个事件")
                    
        except Exception as e:
            logger.error(f"按大小清理缓存失败: {e}")
    
    def _get_cache_size(self) -> int:
        """获取缓存大小（字节）"""
        try:
            total_size = 0
            
            # 遍历缓存目录
            for root, dirs, files in os.walk(self.cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            
            return total_size
            
        except Exception as e:
            logger.error(f"计算缓存大小失败: {e}")
            return 0
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            self.stats['cache_size_mb'] = self._get_cache_size() / (1024 * 1024)
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def get_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 获取事件统计
                    cursor.execute('SELECT COUNT(*) FROM events WHERE synced = 0')
                    pending_count = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(*) FROM events WHERE synced = 1')
                    synced_count = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(*) FROM events')
                    total_count = cursor.fetchone()[0]
            
            return {
                'cache_size_mb': round(self.stats['cache_size_mb'], 2),
                'max_size_gb': self.max_size_gb,
                'usage_percent': round((self.stats['cache_size_mb'] / 1024) / self.max_size_gb * 100, 1),
                'events_total': total_count,
                'events_pending': pending_count,
                'events_synced': synced_count,
                'last_cleanup_time': self.stats['last_cleanup_time'],
                'last_sync_time': self.stats['last_sync_time']
            }
            
        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            return {}
    
    def close(self):
        """关闭缓存"""
        self.running = False
        
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        logger.info("本地缓存已关闭")
