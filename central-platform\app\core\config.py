"""
中央平台配置管理
"""

import os
from typing import List, Optional
from functools import lru_cache

from pydantic import validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 基本配置
    APP_NAME: str = "Cerberus Central Platform"
    VERSION: str = "2.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # 允许的主机
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://cerberus:cerberus123@localhost:5432/cerberus"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_POOL_SIZE: int = 10
    
    # MQTT配置
    MQTT_BROKER_HOST: str = "localhost"
    MQTT_BROKER_PORT: int = 1883
    MQTT_USERNAME: Optional[str] = None
    MQTT_PASSWORD: Optional[str] = None
    MQTT_KEEPALIVE: int = 60
    MQTT_QOS: int = 1
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "cerberus"
    MINIO_SECRET_KEY: str = "cerberus123"
    MINIO_BUCKET: str = "cerberus-videos"
    MINIO_SECURE: bool = False
    
    # 文件上传配置
    MAX_UPLOAD_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_VIDEO_EXTENSIONS: List[str] = [".mp4", ".avi", ".mov", ".mkv"]
    ALLOWED_IMAGE_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".bmp"]
    
    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # 缓存配置
    CACHE_TTL: int = 300  # 5分钟
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/central_platform.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # 数据保留策略
    EVENT_RETENTION_DAYS: int = 90
    VIDEO_RETENTION_DAYS: int = 30
    LOG_RETENTION_DAYS: int = 7
    
    # 监控配置
    HEALTH_CHECK_INTERVAL: int = 30  # 秒
    NODE_OFFLINE_THRESHOLD: int = 120  # 秒
    
    # 通知配置
    EMAIL_ENABLED: bool = False
    EMAIL_SMTP_HOST: str = ""
    EMAIL_SMTP_PORT: int = 587
    EMAIL_USERNAME: str = ""
    EMAIL_PASSWORD: str = ""
    EMAIL_FROM: str = ""
    
    WEBHOOK_ENABLED: bool = False
    WEBHOOK_URL: str = ""
    WEBHOOK_SECRET: str = ""
    
    # 性能配置
    WORKER_PROCESSES: int = 1
    WORKER_CONNECTIONS: int = 1000
    WORKER_TIMEOUT: int = 30
    
    # 前端配置
    FRONTEND_URL: str = "http://localhost:3000"
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v):
        if not v.startswith(("postgresql://", "postgresql+asyncpg://")):
            raise ValueError("DATABASE_URL must be a PostgreSQL URL")
        return v
    
    @validator("REDIS_URL", pre=True)
    def validate_redis_url(cls, v):
        if not v.startswith("redis://"):
            raise ValueError("REDIS_URL must be a Redis URL")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

class DevelopmentSettings(Settings):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"

class ProductionSettings(Settings):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"
    ALLOWED_HOSTS: List[str] = ["your-domain.com"]

class TestingSettings(Settings):
    """测试环境配置"""
    DEBUG: bool = True
    DATABASE_URL: str = "postgresql://test:test@localhost:5432/cerberus_test"
    REDIS_URL: str = "redis://localhost:6379/1"

@lru_cache()
def get_settings() -> Settings:
    """获取配置实例"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()

# MQTT主题配置
class MQTTTopics:
    """MQTT主题配置"""
    
    # 节点状态主题
    NODE_STATUS = "cerberus/nodes/{node_id}/status"
    NODE_STATUS_WILDCARD = "cerberus/nodes/+/status"
    
    # 事件主题
    EVENT = "cerberus/events/{node_id}/{camera_id}"
    EVENT_WILDCARD = "cerberus/events/+/+"
    
    # 配置命令主题
    CONFIG_COMMAND = "cerberus/config/{node_id}/command"
    CONFIG_RESPONSE = "cerberus/config/{node_id}/response"
    
    # 系统主题
    SYSTEM_ALERT = "cerberus/system/alert"
    SYSTEM_HEARTBEAT = "cerberus/system/heartbeat"
    
    @classmethod
    def get_node_status_topic(cls, node_id: str) -> str:
        return cls.NODE_STATUS.format(node_id=node_id)
    
    @classmethod
    def get_event_topic(cls, node_id: str, camera_id: str) -> str:
        return cls.EVENT.format(node_id=node_id, camera_id=camera_id)
    
    @classmethod
    def get_config_command_topic(cls, node_id: str) -> str:
        return cls.CONFIG_COMMAND.format(node_id=node_id)
    
    @classmethod
    def get_config_response_topic(cls, node_id: str) -> str:
        return cls.CONFIG_RESPONSE.format(node_id=node_id)

# 数据库表名配置
class TableNames:
    """数据库表名配置"""
    
    USERS = "users"
    ROLES = "roles"
    PERMISSIONS = "permissions"
    USER_ROLES = "user_roles"
    ROLE_PERMISSIONS = "role_permissions"
    
    NODES = "nodes"
    CAMERAS = "cameras"
    EVENTS = "events"
    EVENT_VIDEOS = "event_videos"
    
    SYSTEM_LOGS = "system_logs"
    AUDIT_LOGS = "audit_logs"
    
    SETTINGS = "settings"
    NOTIFICATIONS = "notifications"

# API响应状态码
class StatusCodes:
    """API响应状态码"""
    
    SUCCESS = 200
    CREATED = 201
    NO_CONTENT = 204
    
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    
    INTERNAL_SERVER_ERROR = 500
    SERVICE_UNAVAILABLE = 503

# 事件类型配置
class EventTypes:
    """事件类型配置"""
    
    OBJECT_DETECTION = "object_detection"
    FACE_RECOGNITION = "face_recognition"
    LINE_CROSSING = "line_crossing"
    CAMERA_TAMPERING = "camera_tampering"
    MOTION_DETECTION = "motion_detection"
    INTRUSION_DETECTION = "intrusion_detection"
    
    @classmethod
    def get_all_types(cls) -> List[str]:
        return [
            cls.OBJECT_DETECTION,
            cls.FACE_RECOGNITION,
            cls.LINE_CROSSING,
            cls.CAMERA_TAMPERING,
            cls.MOTION_DETECTION,
            cls.INTRUSION_DETECTION
        ]

# 用户角色配置
class UserRoles:
    """用户角色配置"""
    
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    
    @classmethod
    def get_all_roles(cls) -> List[str]:
        return [cls.ADMIN, cls.OPERATOR, cls.VIEWER]
