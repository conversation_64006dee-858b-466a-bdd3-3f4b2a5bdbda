{% extends "base.html" %}

{% block title %}仪表盘 - Cerberus 安防监控平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            系统仪表盘
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            在线节点
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.online_nodes }} / {{ stats.total_nodes }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            摄像头总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_cameras }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-video fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            今日事件
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_events_today }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            系统状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <span class="badge bg-success">正常</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和最近事件 -->
<div class="row">
    <!-- 事件趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">事件趋势</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#">查看详情</a>
                        <a class="dropdown-item" href="#">导出数据</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="eventTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 事件类型分布 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">事件类型分布</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="eventTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近事件 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">最近事件</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="recentEventsTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>节点</th>
                                <th>摄像头</th>
                                <th>事件类型</th>
                                <th>置信度</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for event in stats.recent_events %}
                            <tr>
                                <td>{{ event.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>{{ event.node.name }}</td>
                                <td>{{ event.camera.name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ event.event_type }}</span>
                                </td>
                                <td>{{ "%.2f"|format(event.confidence) }}</td>
                                <td>
                                    {% if event.status == 'new' %}
                                        <span class="badge bg-warning">新事件</span>
                                    {% elif event.status == 'reviewed' %}
                                        <span class="badge bg-success">已处理</span>
                                    {% else %}
                                        <span class="badge bg-secondary">已归档</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="/events/{{ event.event_id }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/events" class="btn btn-primary">查看所有事件</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 事件趋势图
const eventTrendCtx = document.getElementById('eventTrendChart').getContext('2d');
const eventTrendChart = new Chart(eventTrendCtx, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        datasets: [{
            label: '事件数量',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 事件类型分布图
const eventTypeCtx = document.getElementById('eventTypeChart').getContext('2d');
const eventTypeChart = new Chart(eventTypeCtx, {
    type: 'doughnut',
    data: {
        labels: ['目标检测', '人脸识别', '越线检测', '摄像头异常'],
        datasets: [{
            data: [45, 25, 20, 10],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 自动刷新数据
setInterval(function() {
    // 这里可以添加AJAX调用来更新数据
    console.log('Refreshing dashboard data...');
}, 30000); // 30秒刷新一次
</script>
{% endblock %}
