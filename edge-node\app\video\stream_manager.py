"""
Cerberus 视频流管理器
整合视频流处理、AI检测和自动录制功能
针对边缘计算设备进行性能优化
"""

import cv2
import numpy as np
import time
import threading
import logging
import queue
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from collections import deque
import psutil

from .video_recorder import RecordingManager, RecordingConfig
from ..ai.detection_engine import DetectionEngine, DetectionResult

logger = logging.getLogger(__name__)

@dataclass
class CameraConfig:
    """摄像头配置"""
    camera_id: str
    name: str
    rtsp_url: str
    enabled: bool = True
    fps: int = 25
    resolution: Optional[tuple] = None
    detection_zones: List[Dict] = None
    detectors: List[str] = None
    auto_reconnect: bool = True
    reconnect_interval: int = 30

@dataclass
class StreamStats:
    """流统计信息"""
    camera_id: str
    status: str  # active, inactive, error, reconnecting
    fps: float = 0.0
    frame_count: int = 0
    error_count: int = 0
    last_frame_time: float = 0
    connection_time: float = 0
    total_detections: int = 0
    recording_status: str = "idle"  # idle, recording, error

class CameraStream:
    """单个摄像头流处理器"""
    
    def __init__(self, config: CameraConfig, detection_engine: DetectionEngine, 
                 recording_manager: RecordingManager):
        self.config = config
        self.detection_engine = detection_engine
        self.recording_manager = recording_manager
        
        # 视频捕获
        self.cap = None
        self.capture_thread = None
        self.running = False
        
        # 帧队列
        self.frame_queue = queue.Queue(maxsize=10)
        self.latest_frame = None
        self.frame_lock = threading.Lock()
        
        # 统计信息
        self.stats = StreamStats(camera_id=config.camera_id, status="inactive")
        self.fps_calculator = deque(maxlen=30)
        
        # 重连机制
        self.reconnect_timer = None
        
        # 事件回调
        self.event_callbacks: List[Callable] = []
        
        logger.info(f"摄像头流初始化完成: {config.camera_id}")
    
    def add_event_callback(self, callback: Callable):
        """添加事件回调"""
        self.event_callbacks.append(callback)
    
    def start(self) -> bool:
        """启动视频流"""
        if self.running:
            logger.warning(f"摄像头 {self.config.camera_id} 已在运行")
            return True
        
        try:
            # 连接摄像头
            if not self._connect_camera():
                return False
            
            self.running = True
            
            # 启动捕获线程
            self.capture_thread = threading.Thread(
                target=self._capture_loop,
                name=f"Capture-{self.config.camera_id}",
                daemon=True
            )
            self.capture_thread.start()
            
            # 启动处理线程
            self.process_thread = threading.Thread(
                target=self._process_loop,
                name=f"Process-{self.config.camera_id}",
                daemon=True
            )
            self.process_thread.start()
            
            self.stats.status = "active"
            self.stats.connection_time = time.time()
            
            logger.info(f"摄像头流启动成功: {self.config.camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动摄像头流失败 {self.config.camera_id}: {e}")
            self.stop()
            return False
    
    def _connect_camera(self) -> bool:
        """连接摄像头"""
        try:
            self.cap = cv2.VideoCapture(self.config.rtsp_url)
            
            # 设置缓冲区大小
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # 设置分辨率
            if self.config.resolution:
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.resolution[0])
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.resolution[1])
            
            # 设置FPS
            self.cap.set(cv2.CAP_PROP_FPS, self.config.fps)
            
            # 测试连接
            ret, frame = self.cap.read()
            if not ret or frame is None:
                raise Exception("无法读取视频帧")
            
            logger.info(f"摄像头连接成功: {self.config.camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"摄像头连接失败 {self.config.camera_id}: {e}")
            if self.cap:
                self.cap.release()
                self.cap = None
            return False
    
    def _capture_loop(self):
        """视频捕获循环"""
        logger.info(f"开始视频捕获: {self.config.camera_id}")
        
        while self.running:
            try:
                if not self.cap or not self.cap.isOpened():
                    if self.config.auto_reconnect:
                        self._schedule_reconnect()
                    break
                
                ret, frame = self.cap.read()
                if not ret or frame is None:
                    self.stats.error_count += 1
                    if self.stats.error_count > 10:  # 连续错误超过10次
                        logger.error(f"摄像头 {self.config.camera_id} 连续读取失败，尝试重连")
                        if self.config.auto_reconnect:
                            self._schedule_reconnect()
                        break
                    continue
                
                # 重置错误计数
                self.stats.error_count = 0
                
                # 更新统计信息
                current_time = time.time()
                self.stats.frame_count += 1
                self.stats.last_frame_time = current_time
                
                # 计算FPS
                self.fps_calculator.append(current_time)
                if len(self.fps_calculator) > 1:
                    time_span = self.fps_calculator[-1] - self.fps_calculator[0]
                    if time_span > 0:
                        self.stats.fps = (len(self.fps_calculator) - 1) / time_span
                
                # 更新最新帧
                with self.frame_lock:
                    self.latest_frame = frame.copy()
                
                # 添加到录制管理器
                self.recording_manager.add_frame(self.config.camera_id, frame)
                
                # 添加到处理队列
                try:
                    self.frame_queue.put_nowait((frame.copy(), current_time))
                except queue.Full:
                    # 队列满时丢弃最旧的帧
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait((frame.copy(), current_time))
                    except queue.Empty:
                        pass
                
            except Exception as e:
                logger.error(f"视频捕获错误 {self.config.camera_id}: {e}")
                self.stats.error_count += 1
                time.sleep(0.1)
        
        logger.info(f"视频捕获结束: {self.config.camera_id}")
    
    def _process_loop(self):
        """视频处理循环"""
        logger.info(f"开始视频处理: {self.config.camera_id}")
        
        while self.running:
            try:
                # 获取帧
                frame, timestamp = self.frame_queue.get(timeout=1.0)
                
                # 执行AI检测
                detections = self.detection_engine.detect(self.config.camera_id, frame)
                
                if detections:
                    self.stats.total_detections += len(detections)
                    self._handle_detections(detections, timestamp)
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"视频处理错误 {self.config.camera_id}: {e}")
                time.sleep(0.1)
        
        logger.info(f"视频处理结束: {self.config.camera_id}")
    
    def _handle_detections(self, detections: List[DetectionResult], timestamp: float):
        """处理检测结果"""
        try:
            # 检查是否需要开始录制
            should_record = any(
                detection.confidence > 0.7 and detection.detection_type in ['object_detection', 'face_recognition']
                for detection in detections
            )
            
            if should_record:
                # 生成事件ID
                event_id = f"{self.config.camera_id}_{int(timestamp * 1000)}"
                
                # 开始录制
                if self.recording_manager.start_recording(self.config.camera_id, event_id):
                    self.stats.recording_status = "recording"
                    logger.info(f"开始事件录制: {event_id}")
                
                # 调用事件回调
                for callback in self.event_callbacks:
                    try:
                        callback(self.config.camera_id, detections, timestamp)
                    except Exception as e:
                        logger.error(f"事件回调失败: {e}")
            
        except Exception as e:
            logger.error(f"处理检测结果失败: {e}")
    
    def _schedule_reconnect(self):
        """安排重连"""
        if self.reconnect_timer:
            return
        
        self.stats.status = "reconnecting"
        logger.info(f"安排重连摄像头: {self.config.camera_id}")
        
        self.reconnect_timer = threading.Timer(
            self.config.reconnect_interval,
            self._attempt_reconnect
        )
        self.reconnect_timer.start()
    
    def _attempt_reconnect(self):
        """尝试重连"""
        self.reconnect_timer = None
        
        if not self.running:
            return
        
        logger.info(f"尝试重连摄像头: {self.config.camera_id}")
        
        # 清理旧连接
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # 尝试重新连接
        if self._connect_camera():
            self.stats.status = "active"
            self.stats.connection_time = time.time()
            self.stats.error_count = 0
            logger.info(f"摄像头重连成功: {self.config.camera_id}")
        else:
            self.stats.status = "error"
            if self.config.auto_reconnect:
                self._schedule_reconnect()
    
    def stop(self):
        """停止视频流"""
        logger.info(f"停止摄像头流: {self.config.camera_id}")
        
        self.running = False
        self.stats.status = "inactive"
        
        # 取消重连定时器
        if self.reconnect_timer:
            self.reconnect_timer.cancel()
            self.reconnect_timer = None
        
        # 等待线程结束
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        if hasattr(self, 'process_thread') and self.process_thread.is_alive():
            self.process_thread.join(timeout=5)
        
        # 释放资源
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # 清空队列
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except queue.Empty:
                break
        
        logger.info(f"摄像头流已停止: {self.config.camera_id}")
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """获取最新帧"""
        with self.frame_lock:
            return self.latest_frame.copy() if self.latest_frame is not None else None
    
    def get_stats(self) -> StreamStats:
        """获取统计信息"""
        return self.stats
    
    def is_active(self) -> bool:
        """检查是否活跃"""
        return self.running and self.stats.status == "active"

class StreamManager:
    """视频流管理器主类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.streams: Dict[str, CameraStream] = {}

        # 初始化检测引擎
        self.detection_engine = DetectionEngine(config)

        # 初始化录制管理器
        recording_config = RecordingConfig(
            pre_buffer_seconds=config.get('PRE_RECORD_SECONDS', 15),
            post_buffer_seconds=config.get('POST_RECORD_SECONDS', 15),
            fps=config.get('VIDEO_FPS', 25),
            resolution=self._parse_resolution(config.get('VIDEO_RESOLUTION', '1280x720')),
            codec=config.get('VIDEO_CODEC', 'mp4v'),
            output_dir=config.get('CACHE_DIR', 'cache') + '/videos',
            max_storage_gb=config.get('CACHE_MAX_SIZE_GB', 10),
            retention_hours=config.get('VIDEO_RETENTION_HOURS', 24)
        )
        self.recording_manager = RecordingManager(recording_config)

        # 事件回调列表
        self.event_callbacks: List[Callable] = []

        # 性能监控
        self.performance_stats = {
            'total_frames_processed': 0,
            'total_detections': 0,
            'total_recordings': 0,
            'start_time': time.time(),
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'gpu_usage': 0.0
        }

        # 启动性能监控
        self._start_performance_monitor()

        logger.info("视频流管理器初始化完成")

    def _parse_resolution(self, resolution_str: str) -> tuple:
        """解析分辨率字符串"""
        try:
            width, height = resolution_str.split('x')
            return (int(width), int(height))
        except:
            return (1280, 720)

    def _start_performance_monitor(self):
        """启动性能监控"""
        def monitor_loop():
            while True:
                try:
                    # CPU使用率
                    self.performance_stats['cpu_usage'] = psutil.cpu_percent(interval=1)

                    # 内存使用率
                    memory = psutil.virtual_memory()
                    self.performance_stats['memory_usage'] = memory.percent

                    # GPU使用率（如果有NVIDIA GPU）
                    try:
                        import pynvml
                        pynvml.nvmlInit()
                        handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                        gpu_util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        self.performance_stats['gpu_usage'] = gpu_util.gpu
                    except:
                        self.performance_stats['gpu_usage'] = 0.0

                    time.sleep(10)  # 每10秒更新一次

                except Exception as e:
                    logger.error(f"性能监控错误: {e}")
                    time.sleep(30)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def add_event_callback(self, callback: Callable):
        """添加事件回调"""
        self.event_callbacks.append(callback)

    def load_cameras_from_config(self):
        """从配置加载摄像头"""
        cameras_config = self.config.get('cameras', {})

        for camera_id, camera_config in cameras_config.items():
            if camera_config.get('enabled', False):
                try:
                    config = CameraConfig(
                        camera_id=camera_id,
                        name=camera_config.get('name', camera_id),
                        rtsp_url=camera_config.get('rtsp_url', ''),
                        enabled=camera_config.get('enabled', True),
                        fps=camera_config.get('fps', self.config.get('VIDEO_FPS', 25)),
                        resolution=self._parse_resolution(
                            camera_config.get('resolution', self.config.get('VIDEO_RESOLUTION', '1280x720'))
                        ),
                        detection_zones=camera_config.get('detection_zones', []),
                        detectors=camera_config.get('detectors', []),
                        auto_reconnect=camera_config.get('auto_reconnect', True),
                        reconnect_interval=camera_config.get('reconnect_interval', 30)
                    )

                    self.add_camera(config)

                except Exception as e:
                    logger.error(f"加载摄像头配置失败 {camera_id}: {e}")

    def add_camera(self, config: CameraConfig) -> bool:
        """添加摄像头"""
        try:
            if config.camera_id in self.streams:
                logger.warning(f"摄像头 {config.camera_id} 已存在")
                return False

            # 创建摄像头流
            stream = CameraStream(config, self.detection_engine, self.recording_manager)

            # 添加事件回调
            for callback in self.event_callbacks:
                stream.add_event_callback(callback)

            self.streams[config.camera_id] = stream

            logger.info(f"摄像头添加成功: {config.camera_id}")
            return True

        except Exception as e:
            logger.error(f"添加摄像头失败 {config.camera_id}: {e}")
            return False

    def remove_camera(self, camera_id: str) -> bool:
        """移除摄像头"""
        try:
            if camera_id not in self.streams:
                logger.warning(f"摄像头 {camera_id} 不存在")
                return False

            # 停止流
            self.streams[camera_id].stop()

            # 移除
            del self.streams[camera_id]

            logger.info(f"摄像头移除成功: {camera_id}")
            return True

        except Exception as e:
            logger.error(f"移除摄像头失败 {camera_id}: {e}")
            return False

    def start_camera(self, camera_id: str) -> bool:
        """启动摄像头"""
        if camera_id not in self.streams:
            logger.error(f"摄像头 {camera_id} 不存在")
            return False

        return self.streams[camera_id].start()

    def stop_camera(self, camera_id: str) -> bool:
        """停止摄像头"""
        if camera_id not in self.streams:
            logger.error(f"摄像头 {camera_id} 不存在")
            return False

        self.streams[camera_id].stop()
        return True

    def start_all(self):
        """启动所有摄像头"""
        logger.info("启动所有摄像头")
        for camera_id in self.streams:
            self.start_camera(camera_id)

    def stop_all(self):
        """停止所有摄像头"""
        logger.info("停止所有摄像头")
        for camera_id in list(self.streams.keys()):
            self.stop_camera(camera_id)

    def get_cameras(self) -> Dict[str, Dict[str, Any]]:
        """获取摄像头列表"""
        cameras = {}
        for camera_id, stream in self.streams.items():
            stats = stream.get_stats()
            cameras[camera_id] = {
                'camera_id': camera_id,
                'name': stream.config.name,
                'status': stats.status,
                'fps': stats.fps,
                'frame_count': stats.frame_count,
                'error_count': stats.error_count,
                'last_frame_time': stats.last_frame_time,
                'total_detections': stats.total_detections,
                'recording_status': stats.recording_status,
                'is_active': stream.is_active()
            }
        return cameras

    def get_camera_status(self) -> Dict[str, str]:
        """获取摄像头状态"""
        return {camera_id: stream.get_stats().status for camera_id, stream in self.streams.items()}

    def get_active_cameras(self) -> List[str]:
        """获取活跃的摄像头列表"""
        return [camera_id for camera_id, stream in self.streams.items() if stream.is_active()]

    def get_latest_frame(self, camera_id: str) -> Optional[np.ndarray]:
        """获取最新帧"""
        if camera_id in self.streams:
            return self.streams[camera_id].get_latest_frame()
        return None

    def record_event_video(self, camera_id: str, event_id: str, detection: Dict[str, Any]) -> Optional[str]:
        """录制事件视频"""
        try:
            if self.recording_manager.start_recording(camera_id, event_id):
                # 返回预期的文件路径
                timestamp_str = time.strftime('%Y%m%d_%H%M%S')
                filename = f"{camera_id}_{event_id}_{timestamp_str}.mp4"
                return os.path.join(self.recording_manager.config.output_dir, filename)
            return None
        except Exception as e:
            logger.error(f"录制事件视频失败: {e}")
            return None

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        # 更新统计信息
        total_detections = sum(stream.get_stats().total_detections for stream in self.streams.values())
        self.performance_stats['total_detections'] = total_detections

        recording_stats = self.recording_manager.get_recording_stats()

        return {
            'performance': self.performance_stats.copy(),
            'cameras': {
                'total': len(self.streams),
                'active': len(self.get_active_cameras()),
                'inactive': len(self.streams) - len(self.get_active_cameras())
            },
            'detection': self.detection_engine.get_stats(),
            'recording': recording_stats,
            'uptime': time.time() - self.performance_stats['start_time']
        }

    def cleanup(self):
        """清理所有资源"""
        logger.info("开始清理视频流管理器")

        # 停止所有摄像头
        self.stop_all()

        # 清理录制管理器
        self.recording_manager.cleanup()

        # 清理检测引擎
        self.detection_engine.cleanup()

        # 清空流列表
        self.streams.clear()

        logger.info("视频流管理器清理完成")
