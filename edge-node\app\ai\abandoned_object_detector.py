"""
遗留物体检测器
检测在画面中停留时间超过阈值的物体，如遗留包裹、丢弃物品等
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque

from .detection_engine import BaseDetector, DetectionResult

logger = logging.getLogger(__name__)

@dataclass
class TrackedObject:
    """被跟踪的物体"""
    object_id: int
    bbox: Tuple[int, int, int, int]
    class_name: str
    confidence: float
    first_seen: float
    last_seen: float
    stationary_duration: float
    position_history: deque
    is_abandoned: bool = False

class AbandonedObjectDetector(BaseDetector):
    """遗留物体检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 配置参数
        self.abandon_threshold = config.get('abandon_threshold', 30.0)  # 秒
        self.movement_threshold = config.get('movement_threshold', 20)  # 像素
        self.min_object_size = config.get('min_object_size', 500)  # 最小像素面积
        self.track_retention_time = config.get('track_retention_time', 300)  # 跟踪保持时间（秒）
        
        # 目标类别过滤
        self.target_classes = config.get('target_classes', [
            'handbag', 'backpack', 'suitcase', 'umbrella', 'bottle', 
            'laptop', 'book', 'cell phone', 'bicycle'
        ])
        
        # 排除区域（如垃圾桶附近、座椅等正常放置物品的区域）
        self.exclude_zones = config.get('exclude_zones', [])
        
        # 跟踪数据
        self.tracked_objects: Dict[int, TrackedObject] = {}
        self.next_object_id = 1
        self.abandoned_objects: Dict[int, TrackedObject] = {}
        
        # 背景减除器用于检测静态物体
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=500, varThreshold=50, detectShadows=True
        )
        
        logger.info("遗留物体检测器初始化完成")
    
    def load_model(self) -> bool:
        """加载模型（使用传统CV方法，不需要深度学习模型）"""
        return True
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行遗留物体检测"""
        if not self.enabled:
            return []
        
        current_time = time.time()
        object_detections = kwargs.get('object_detections', [])
        
        # 更新背景模型
        fg_mask = self.bg_subtractor.apply(frame)
        
        # 过滤相关物体检测结果
        relevant_objects = self._filter_relevant_objects(object_detections)
        
        # 更新物体跟踪
        self._update_tracking(relevant_objects, current_time)
        
        # 检测遗留物体
        abandoned_results = self._detect_abandoned_objects(current_time)
        
        # 清理过期的跟踪数据
        self._cleanup_expired_tracks(current_time)
        
        return abandoned_results
    
    def _filter_relevant_objects(self, detections: List[DetectionResult]) -> List[DetectionResult]:
        """过滤相关的物体检测结果"""
        filtered = []
        
        for detection in detections:
            # 检查是否是目标类别
            if detection.class_name not in self.target_classes:
                continue
            
            # 检查物体大小
            if detection.bbox:
                x1, y1, x2, y2 = detection.bbox
                area = (x2 - x1) * (y2 - y1)
                if area < self.min_object_size:
                    continue
            
            # 检查是否在排除区域
            if self._is_in_exclude_zone(detection.bbox):
                continue
            
            filtered.append(detection)
        
        return filtered
    
    def _is_in_exclude_zone(self, bbox: Tuple[int, int, int, int]) -> bool:
        """检查物体是否在排除区域内"""
        if not bbox or not self.exclude_zones:
            return False
        
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        for zone in self.exclude_zones:
            zone_x1, zone_y1, zone_x2, zone_y2 = zone
            if zone_x1 <= center_x <= zone_x2 and zone_y1 <= center_y <= zone_y2:
                return True
        
        return False
    
    def _update_tracking(self, detections: List[DetectionResult], current_time: float):
        """更新物体跟踪"""
        if not detections:
            return
        
        # 计算检测结果与现有跟踪的匹配
        matches, unmatched_detections, unmatched_tracks = self._match_detections_to_tracks(detections)
        
        # 更新匹配的跟踪
        for detection_idx, track_id in matches:
            detection = detections[detection_idx]
            tracked_obj = self.tracked_objects[track_id]
            
            # 更新位置和时间
            old_bbox = tracked_obj.bbox
            new_bbox = detection.bbox
            
            tracked_obj.bbox = new_bbox
            tracked_obj.last_seen = current_time
            tracked_obj.confidence = detection.confidence
            
            # 计算移动距离
            movement = self._calculate_movement(old_bbox, new_bbox)
            
            # 更新位置历史
            tracked_obj.position_history.append({
                'bbox': new_bbox,
                'timestamp': current_time,
                'movement': movement
            })
            
            # 如果移动距离小于阈值，增加静止时间
            if movement < self.movement_threshold:
                tracked_obj.stationary_duration += (current_time - tracked_obj.last_seen)
            else:
                # 如果物体移动了，重置静止时间
                tracked_obj.stationary_duration = 0
                tracked_obj.is_abandoned = False
        
        # 创建新的跟踪
        for detection_idx in unmatched_detections:
            detection = detections[detection_idx]
            self._create_new_track(detection, current_time)
        
        # 标记消失的跟踪
        for track_id in unmatched_tracks:
            # 暂时保留，等待可能的重新匹配
            pass
    
    def _match_detections_to_tracks(self, detections: List[DetectionResult]) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """将检测结果匹配到现有跟踪"""
        if not self.tracked_objects:
            return [], list(range(len(detections))), []
        
        # 计算IoU矩阵
        detection_boxes = [det.bbox for det in detections if det.bbox]
        track_boxes = [track.bbox for track in self.tracked_objects.values()]
        track_ids = list(self.tracked_objects.keys())
        
        if not detection_boxes or not track_boxes:
            return [], list(range(len(detections))), track_ids
        
        iou_matrix = self._compute_iou_matrix(detection_boxes, track_boxes)
        
        # 简单的贪婪匹配算法
        matches = []
        used_detections = set()
        used_tracks = set()
        
        iou_threshold = 0.3
        
        for i in range(len(detection_boxes)):
            for j in range(len(track_boxes)):
                if i in used_detections or j in used_tracks:
                    continue
                
                if iou_matrix[i][j] > iou_threshold:
                    matches.append((i, track_ids[j]))
                    used_detections.add(i)
                    used_tracks.add(j)
                    break
        
        unmatched_detections = [i for i in range(len(detections)) if i not in used_detections]
        unmatched_tracks = [track_ids[j] for j in range(len(track_ids)) if j not in used_tracks]
        
        return matches, unmatched_detections, unmatched_tracks
    
    def _compute_iou_matrix(self, boxes1: List[Tuple[int, int, int, int]], 
                           boxes2: List[Tuple[int, int, int, int]]) -> np.ndarray:
        """计算IoU矩阵"""
        matrix = np.zeros((len(boxes1), len(boxes2)))
        
        for i, box1 in enumerate(boxes1):
            for j, box2 in enumerate(boxes2):
                matrix[i, j] = self._calculate_iou(box1, box2)
        
        return matrix
    
    def _calculate_iou(self, box1: Tuple[int, int, int, int], 
                      box2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_movement(self, old_bbox: Tuple[int, int, int, int], 
                           new_bbox: Tuple[int, int, int, int]) -> float:
        """计算物体移动距离"""
        if not old_bbox or not new_bbox:
            return 0.0
        
        old_center_x = (old_bbox[0] + old_bbox[2]) // 2
        old_center_y = (old_bbox[1] + old_bbox[3]) // 2
        new_center_x = (new_bbox[0] + new_bbox[2]) // 2
        new_center_y = (new_bbox[1] + new_bbox[3]) // 2
        
        return np.sqrt((new_center_x - old_center_x)**2 + (new_center_y - old_center_y)**2)
    
    def _create_new_track(self, detection: DetectionResult, current_time: float):
        """创建新的跟踪"""
        tracked_obj = TrackedObject(
            object_id=self.next_object_id,
            bbox=detection.bbox,
            class_name=detection.class_name,
            confidence=detection.confidence,
            first_seen=current_time,
            last_seen=current_time,
            stationary_duration=0.0,
            position_history=deque(maxlen=100)
        )
        
        tracked_obj.position_history.append({
            'bbox': detection.bbox,
            'timestamp': current_time,
            'movement': 0.0
        })
        
        self.tracked_objects[self.next_object_id] = tracked_obj
        self.next_object_id += 1
    
    def _detect_abandoned_objects(self, current_time: float) -> List[DetectionResult]:
        """检测遗留物体"""
        results = []
        
        for track_id, tracked_obj in self.tracked_objects.items():
            # 检查是否超过遗留阈值
            if (tracked_obj.stationary_duration >= self.abandon_threshold and 
                not tracked_obj.is_abandoned):
                
                tracked_obj.is_abandoned = True
                self.abandoned_objects[track_id] = tracked_obj
                
                # 创建检测结果
                result = DetectionResult(
                    detection_type='abandoned_object',
                    confidence=0.9,
                    bbox=tracked_obj.bbox,
                    timestamp=current_time,
                    class_name=tracked_obj.class_name,
                    metadata={
                        'object_id': track_id,
                        'abandon_duration': tracked_obj.stationary_duration,
                        'first_seen': tracked_obj.first_seen,
                        'object_class': tracked_obj.class_name,
                        'area': self._calculate_area(tracked_obj.bbox),
                        'abandon_threshold': self.abandon_threshold
                    }
                )
                results.append(result)
                
                logger.warning(
                    f"检测到遗留物体: ID={track_id}, "
                    f"类别={tracked_obj.class_name}, "
                    f"静止时间={tracked_obj.stationary_duration:.1f}秒"
                )
        
        return results
    
    def _calculate_area(self, bbox: Tuple[int, int, int, int]) -> int:
        """计算边界框面积"""
        if not bbox:
            return 0
        x1, y1, x2, y2 = bbox
        return (x2 - x1) * (y2 - y1)
    
    def _cleanup_expired_tracks(self, current_time: float):
        """清理过期的跟踪数据"""
        expired_tracks = []
        
        for track_id, tracked_obj in self.tracked_objects.items():
            # 如果物体消失时间过长，删除跟踪
            if current_time - tracked_obj.last_seen > self.track_retention_time:
                expired_tracks.append(track_id)
        
        for track_id in expired_tracks:
            del self.tracked_objects[track_id]
            if track_id in self.abandoned_objects:
                del self.abandoned_objects[track_id]
    
    def get_tracking_info(self) -> Dict[str, Any]:
        """获取跟踪信息"""
        return {
            'total_tracked_objects': len(self.tracked_objects),
            'abandoned_objects': len(self.abandoned_objects),
            'tracked_objects': {
                track_id: {
                    'class_name': obj.class_name,
                    'stationary_duration': obj.stationary_duration,
                    'is_abandoned': obj.is_abandoned,
                    'bbox': obj.bbox
                }
                for track_id, obj in self.tracked_objects.items()
            }
        }
    
    def get_detector_type(self) -> str:
        return "abandoned_object" 