# Cerberus 分布式智能安防监控平台 - 完整部署指南

## 📋 目录

1. [系统要求](#系统要求)
2. [快速部署](#快速部署)
3. [开发环境部署](#开发环境部署)
4. [生产环境部署](#生产环境部署)
5. [配置说明](#配置说明)
6. [服务验证](#服务验证)
7. [故障排除](#故障排除)
8. [性能优化](#性能优化)
9. [安全配置](#安全配置)
10. [监控和维护](#监控和维护)

## 🖥️ 系统要求

### 最低硬件要求

#### 开发环境
- **CPU**: 2核心 2.0GHz+
- **内存**: 8GB RAM
- **存储**: 50GB 可用空间
- **网络**: 100Mbps

#### 生产环境 - 中央平台
- **CPU**: 4核心 2.5GHz+
- **内存**: 16GB RAM
- **存储**: 200GB SSD (系统) + 1TB+ (数据存储)
- **网络**: 1Gbps

#### 生产环境 - 边缘节点
- **CPU**: 2核心 2.0GHz+ (支持AVX指令集)
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **GPU**: 可选，NVIDIA GPU with CUDA 11.0+
- **网络**: 100Mbps+

### 软件要求

```bash
# 必需软件
- Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.25+

# 生产环境额外要求
- Kubernetes 1.24+
- kubectl
- Helm 3.0+ (可选)
```

## ⚡ 快速部署

### 一键启动开发环境

```bash
# 1. 克隆项目
git clone https://github.com/ThomasLXZ/CameraDetection.git
cd CameraDetection

# 2. 复制配置文件
cp .env.example .env

# 3. 启动所有服务
docker-compose up -d

# 4. 等待服务启动 (约2-3分钟)
docker-compose logs -f

# 5. 访问系统
echo "🎉 部署完成！"
echo "Web管理界面: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo "MQTT管理: http://localhost:18083 (admin/public)"
echo "MinIO管理: http://localhost:9001 (cerberus/cerberus123)"
```

### 验证部署

```bash
# 检查所有服务状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:8000/health
curl http://localhost:5000/health
```

## 🔧 开发环境部署

### 步骤1: 环境准备

```bash
# 安装Docker和Docker Compose (Ubuntu)
sudo apt update
sudo apt install -y docker.io docker-compose
sudo usermod -aG docker $USER
newgrp docker

# 验证安装
docker --version
docker-compose --version
```

### 步骤2: 项目配置

```bash
# 克隆项目
git clone https://github.com/ThomasLXZ/CameraDetection.git
cd CameraDetection

# 创建配置文件
cp .env.example .env

# 编辑配置 (可选)
vim .env
```

### 步骤3: 启动基础设施

```bash
# 启动数据库和消息队列
docker-compose up -d postgres redis mqtt minio

# 等待服务就绪
sleep 30

# 检查服务状态
docker-compose ps
```

### 步骤4: 初始化数据库

```bash
# 进入PostgreSQL容器
docker-compose exec postgres psql -U cerberus -d cerberus

# 验证数据库初始化
\dt

# 退出
\q
```

### 步骤5: 启动应用服务

```bash
# 启动中央平台
docker-compose up -d central-platform

# 启动边缘节点
docker-compose up -d edge-node-1

# 查看日志
docker-compose logs -f central-platform
docker-compose logs -f edge-node-1
```

### 步骤6: 本地开发模式 (可选)

```bash
# 中央平台本地开发
cd central-platform
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 运行数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 边缘节点本地开发 (新终端)
cd edge-node
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 启动边缘节点
python app.py
```

## 🏭 生产环境部署

### 方案1: Docker Compose 生产部署

#### 1. 服务器准备

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash cerberus
sudo usermod -aG docker cerberus

# 创建目录结构
sudo mkdir -p /opt/cerberus/{data,logs,config,backups}
sudo chown -R cerberus:cerberus /opt/cerberus

# 切换到cerberus用户
sudo su - cerberus
```

#### 2. 部署应用

```bash
# 克隆代码到生产目录
cd /opt/cerberus
git clone https://github.com/ThomasLXZ/CameraDetection.git app
cd app

# 配置生产环境
cp .env.example .env.prod
vim .env.prod
```

#### 3. 生产环境配置文件

```bash
# .env.prod 示例
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-super-secure-secret-key-here

# 数据库配置
DATABASE_URL=postgresql://cerberus:secure-password@localhost:5432/cerberus

# Redis配置
REDIS_URL=redis://localhost:6379/0

# MQTT配置
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=cerberus
MINIO_SECRET_KEY=secure-minio-password

# 安全配置
ALLOWED_HOSTS=your-domain.com,your-ip-address

# 性能配置
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
```

#### 4. 启动生产服务

```bash
# 使用生产配置启动
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 设置开机自启
sudo systemctl enable docker
```

### 方案2: Kubernetes 生产部署

#### 1. 准备Kubernetes集群

```bash
# 验证集群连接
kubectl cluster-info
kubectl get nodes

# 创建命名空间
kubectl apply -f k8s/namespace.yaml
```

#### 2. 配置Secrets

```bash
# 创建数据库密码
kubectl create secret generic postgres-secret \
  --from-literal=password=your-secure-db-password \
  -n cerberus

# 创建应用密钥
kubectl create secret generic app-secret \
  --from-literal=secret-key=your-super-secure-secret-key \
  --from-literal=minio-access-key=cerberus \
  --from-literal=minio-secret-key=your-secure-minio-password \
  -n cerberus
```

#### 3. 部署基础设施

```bash
# 部署PostgreSQL
kubectl apply -f k8s/postgres.yaml

# 部署Redis
kubectl apply -f k8s/redis.yaml

# 部署MQTT
kubectl apply -f k8s/mqtt.yaml

# 部署MinIO
kubectl apply -f k8s/minio.yaml

# 等待服务就绪
kubectl wait --for=condition=ready pod -l app=postgres -n cerberus --timeout=300s
```

#### 4. 部署应用

```bash
# 部署中央平台
kubectl apply -f k8s/central-platform.yaml

# 部署边缘节点
kubectl apply -f k8s/edge-node.yaml

# 检查部署状态
kubectl get pods -n cerberus
kubectl get services -n cerberus
```

#### 5. 配置Ingress (可选)

```bash
# 安装NGINX Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 应用Ingress配置
kubectl apply -f k8s/ingress.yaml
```

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `ENVIRONMENT` | 环境类型 | development | 否 |
| `DEBUG` | 调试模式 | false | 否 |
| `SECRET_KEY` | 应用密钥 | - | 是 |
| `DATABASE_URL` | 数据库连接 | - | 是 |
| `REDIS_URL` | Redis连接 | - | 是 |
| `MQTT_BROKER_HOST` | MQTT服务器 | localhost | 是 |
| `MINIO_ENDPOINT` | MinIO端点 | localhost:9000 | 是 |

### 摄像头配置

```json
// config/cameras.json
{
  "camera_1": {
    "name": "前门摄像头",
    "rtsp_url": "rtsp://admin:password@*************:554/stream1",
    "enabled": true,
    "detection_zones": [
      {
        "name": "entrance",
        "polygon": [[0, 0], [100, 0], [100, 100], [0, 100]]
      }
    ],
    "detectors": ["object_detection", "face_recognition"]
  }
}
```

### AI检测器配置

```json
// config/detectors.json
{
  "object_detection": {
    "model_path": "models/yolov8n.onnx",
    "confidence_threshold": 0.5,
    "nms_threshold": 0.4,
    "target_classes": ["person", "car", "truck", "bicycle"],
    "enabled": true
  }
}
```

## ✅ 服务验证

### 健康检查脚本

```bash
#!/bin/bash
# health_check.sh

echo "🔍 Cerberus 系统健康检查"
echo "=========================="

# 检查中央平台
echo "检查中央平台..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 中央平台: 正常"
else
    echo "❌ 中央平台: 异常"
fi

# 检查边缘节点
echo "检查边缘节点..."
if curl -s http://localhost:5000/health > /dev/null; then
    echo "✅ 边缘节点: 正常"
else
    echo "❌ 边缘节点: 异常"
fi

# 检查数据库
echo "检查数据库..."
if docker-compose exec -T postgres pg_isready -U cerberus > /dev/null; then
    echo "✅ PostgreSQL: 正常"
else
    echo "❌ PostgreSQL: 异常"
fi

# 检查Redis
echo "检查Redis..."
if docker-compose exec -T redis redis-cli ping > /dev/null; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 异常"
fi

# 检查MQTT
echo "检查MQTT..."
if docker-compose exec -T mqtt emqx_ctl status > /dev/null; then
    echo "✅ MQTT: 正常"
else
    echo "❌ MQTT: 异常"
fi

echo "=========================="
echo "健康检查完成"
```

### 功能测试

```bash
# 测试API接口
curl -X GET http://localhost:8000/api/v1/nodes
curl -X GET http://localhost:8000/api/v1/events

# 测试MQTT连接
mosquitto_pub -h localhost -t test -m "hello world"

# 测试MinIO连接
mc alias set local http://localhost:9000 cerberus cerberus123
mc ls local
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败

**问题**: Docker容器无法启动
```bash
# 检查容器状态
docker-compose ps
docker-compose logs [service-name]

# 常见解决方案
docker-compose down
docker-compose up -d
```

**问题**: 端口冲突
```bash
# 检查端口占用
sudo netstat -tulpn | grep :8000

# 修改端口配置
vim docker-compose.yml
# 或修改 .env 文件中的端口配置
```

#### 2. 数据库连接问题

**问题**: 无法连接PostgreSQL
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U cerberus

# 重置数据库
docker-compose down postgres
docker volume rm cerberus_postgres_data
docker-compose up -d postgres
```

**问题**: 数据库初始化失败
```bash
# 手动执行初始化脚本
docker-compose exec postgres psql -U cerberus -d cerberus -f /docker-entrypoint-initdb.d/init.sql
```

#### 3. MQTT连接问题

**问题**: MQTT服务无法连接
```bash
# 检查MQTT服务状态
docker-compose exec mqtt emqx_ctl status

# 重启MQTT服务
docker-compose restart mqtt

# 检查MQTT日志
docker-compose logs mqtt
```

#### 4. 视频流处理问题

**问题**: 摄像头连接失败
```bash
# 检查网络连接
ping [camera-ip]
telnet [camera-ip] 554

# 验证RTSP流
ffplay rtsp://admin:password@*************:554/stream1
```

**问题**: AI检测模型加载失败
```bash
# 检查模型文件
ls -la edge-node/models/

# 下载默认模型
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.onnx -O edge-node/models/yolov8n.onnx
```

#### 5. 性能问题

**问题**: 系统响应慢
```bash
# 检查系统资源
docker stats
htop

# 检查数据库性能
docker-compose exec postgres psql -U cerberus -d cerberus -c "SELECT * FROM pg_stat_activity;"
```

### 日志分析

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs -f central-platform
docker-compose logs -f edge-node-1

# 查看系统日志
journalctl -u docker
tail -f /var/log/syslog
```

## 🚀 性能优化

### 数据库优化

```sql
-- PostgreSQL性能优化配置
-- /etc/postgresql/14/main/postgresql.conf

# 内存配置
shared_buffers = 256MB          # 25% of RAM
effective_cache_size = 1GB      # 75% of RAM
work_mem = 4MB
maintenance_work_mem = 64MB

# 连接配置
max_connections = 200
max_prepared_transactions = 200

# 检查点配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# 查询优化
random_page_cost = 1.1
effective_io_concurrency = 200
```

### Redis优化

```bash
# Redis配置优化
# /etc/redis/redis.conf

# 内存配置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0
```

### 应用优化

```bash
# 中央平台性能配置
# .env 文件

# 工作进程数 (CPU核心数)
WORKER_PROCESSES=4

# 数据库连接池
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis连接池
REDIS_POOL_SIZE=20

# 缓存配置
CACHE_TTL=300
```

### 系统优化

```bash
# 系统内核参数优化
# /etc/sysctl.conf

# 网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# 文件系统优化
fs.file-max = 65536

# 应用生效
sudo sysctl -p
```

## 🔒 安全配置

### 网络安全

```bash
# 防火墙配置 (UFW)
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow 22/tcp          # SSH
sudo ufw allow 80/tcp          # HTTP
sudo ufw allow 443/tcp         # HTTPS
sudo ufw allow 8000/tcp        # 中央平台
sudo ufw allow 1883/tcp        # MQTT

# 限制SSH访问
sudo ufw limit ssh
```

### SSL/TLS配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 应用安全

```bash
# 生成安全密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 配置环境变量
export SECRET_KEY="your-generated-secret-key"
export DATABASE_PASSWORD="secure-database-password"
export MINIO_SECRET_KEY="secure-minio-password"
```

### 访问控制

```yaml
# nginx配置示例
# /etc/nginx/sites-available/cerberus

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 限制访问
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # IP白名单 (可选)
        # allow ***********/24;
        # deny all;
    }
}
```

## 📊 监控和维护

### 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 实时监控脚本
#!/bin/bash
# monitor.sh

while true; do
    clear
    echo "=== Cerberus 系统监控 ==="
    echo "时间: $(date)"
    echo

    echo "=== 容器状态 ==="
    docker-compose ps
    echo

    echo "=== 系统资源 ==="
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
    echo "内存使用率:"
    free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'
    echo "磁盘使用率:"
    df -h / | awk 'NR==2{printf "%s\n", $5}'
    echo

    echo "=== 网络连接 ==="
    netstat -an | grep :8000 | wc -l
    echo

    sleep 30
done
```

### 日志管理

```bash
# 日志轮转配置
# /etc/logrotate.d/cerberus

/opt/cerberus/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cerberus cerberus
    postrotate
        docker-compose restart central-platform edge-node-1
    endscript
}
```

### 备份策略

```bash
#!/bin/bash
# backup.sh - 自动备份脚本

BACKUP_DIR="/opt/cerberus/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
echo "备份数据库..."
docker-compose exec -T postgres pg_dump -U cerberus cerberus > $BACKUP_DIR/database_$DATE.sql

# 配置文件备份
echo "备份配置文件..."
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/cerberus/app/config/

# MinIO数据备份
echo "备份MinIO数据..."
mc mirror local/cerberus-videos $BACKUP_DIR/videos_$DATE/

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "videos_*" -mtime +7 -exec rm -rf {} \;

echo "备份完成: $DATE"
```

### 自动化维护

```bash
# 添加到crontab
crontab -e

# 每日备份 (凌晨2点)
0 2 * * * /opt/cerberus/scripts/backup.sh

# 每周重启服务 (周日凌晨3点)
0 3 * * 0 cd /opt/cerberus/app && docker-compose restart

# 每月清理日志 (每月1号凌晨4点)
0 4 1 * * find /opt/cerberus/logs -name "*.log" -mtime +30 -delete

# 每日健康检查 (每小时)
0 * * * * /opt/cerberus/scripts/health_check.sh
```

## 📞 技术支持

### 获取帮助

- **GitHub Issues**: https://github.com/ThomasLXZ/CameraDetection/issues
- **文档**: 查看 `docs/` 目录下的其他文档
- **日志分析**: 使用 `docker-compose logs` 查看详细日志

### 常用命令速查

```bash
# 服务管理
docker-compose up -d          # 启动所有服务
docker-compose down           # 停止所有服务
docker-compose restart       # 重启所有服务
docker-compose ps            # 查看服务状态

# 日志查看
docker-compose logs -f       # 查看所有日志
docker-compose logs [service] # 查看特定服务日志

# 数据库操作
docker-compose exec postgres psql -U cerberus -d cerberus

# 系统监控
docker stats                 # 容器资源使用
htop                         # 系统资源监控
```

---

**🎉 恭喜！您已成功部署Cerberus分布式智能安防监控平台！**

如有任何问题，请参考故障排除部分或提交GitHub Issue。
