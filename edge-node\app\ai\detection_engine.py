"""
Cerberus 检测引擎 - 可插拔的多算法检测框架
支持目标检测、人脸识别、越线检测、摄像头遮挡检测
针对边缘计算设备（如NVIDIA Jetson Nano）进行性能优化
"""

import cv2
import numpy as np
import time
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass
from collections import deque
import onnxruntime as ort

logger = logging.getLogger(__name__)

@dataclass
class DetectionResult:
    """检测结果数据类"""
    detection_type: str
    confidence: float
    bbox: Optional[Tuple[int, int, int, int]]  # (x1, y1, x2, y2)
    timestamp: float
    metadata: Dict[str, Any]
    class_name: Optional[str] = None
    track_id: Optional[int] = None

class BaseDetector(ABC):
    """检测器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get('enabled', True)
        self.confidence_threshold = config.get('confidence_threshold', 0.5)
        self.model_path = config.get('model_path', '')
        self.session = None
        self.input_shape = None
        self.output_names = None
        
    @abstractmethod
    def load_model(self) -> bool:
        """加载模型"""
        pass
    
    @abstractmethod
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行检测"""
        pass
    
    @abstractmethod
    def get_detector_type(self) -> str:
        """获取检测器类型"""
        pass
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """预处理帧"""
        return frame
    
    def postprocess_results(self, outputs: Any, frame_shape: Tuple[int, int]) -> List[DetectionResult]:
        """后处理结果"""
        return []

class ObjectDetector(BaseDetector):
    """目标检测器 - 基于YOLO"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.target_classes = config.get('target_classes', ['person', 'car', 'truck', 'bicycle'])
        self.nms_threshold = config.get('nms_threshold', 0.4)
        self.input_size = config.get('input_size', (640, 640))
        self.class_names = self._load_class_names()
        
    def _load_class_names(self) -> List[str]:
        """加载类别名称"""
        # COCO数据集类别名称
        return [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    
    def load_model(self) -> bool:
        """加载YOLO模型"""
        try:
            # 配置ONNX Runtime
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            self.input_shape = self.session.get_inputs()[0].shape
            
            logger.info(f"目标检测模型加载成功: {self.model_path}")
            logger.info(f"输入形状: {self.input_shape}")
            logger.info(f"使用提供者: {self.session.get_providers()}")
            return True
            
        except Exception as e:
            logger.error(f"目标检测模型加载失败: {e}")
            return False
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """预处理帧"""
        # 调整大小并保持宽高比
        h, w = frame.shape[:2]
        scale = min(self.input_size[0] / w, self.input_size[1] / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # 调整大小
        resized = cv2.resize(frame, (new_w, new_h))
        
        # 填充到目标大小
        padded = np.full((self.input_size[1], self.input_size[0], 3), 114, dtype=np.uint8)
        padded[:new_h, :new_w] = resized
        
        # 转换为模型输入格式
        input_tensor = padded.astype(np.float32) / 255.0
        input_tensor = np.transpose(input_tensor, (2, 0, 1))  # HWC -> CHW
        input_tensor = np.expand_dims(input_tensor, axis=0)  # 添加batch维度
        
        return input_tensor
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行目标检测"""
        if not self.enabled or self.session is None:
            return []
        
        try:
            # 预处理
            input_tensor = self.preprocess_frame(frame)
            
            # 推理
            outputs = self.session.run(self.output_names, {self.input_name: input_tensor})
            
            # 后处理
            return self.postprocess_results(outputs[0], frame.shape[:2])
            
        except Exception as e:
            logger.error(f"目标检测失败: {e}")
            return []
    
    def postprocess_results(self, outputs: np.ndarray, frame_shape: Tuple[int, int]) -> List[DetectionResult]:
        """后处理YOLO输出"""
        results = []
        h, w = frame_shape
        
        # 计算缩放比例
        scale_x = w / self.input_size[0]
        scale_y = h / self.input_size[1]
        
        # 解析输出 (假设YOLOv8格式: [batch, 84, 8400])
        if len(outputs.shape) == 3:
            outputs = outputs[0].T  # [8400, 84]
        
        # 提取边界框和置信度
        boxes = outputs[:, :4]  # x_center, y_center, width, height
        scores = outputs[:, 4:4+len(self.class_names)]
        
        # 转换为xyxy格式
        x1 = (boxes[:, 0] - boxes[:, 2] / 2) * scale_x
        y1 = (boxes[:, 1] - boxes[:, 3] / 2) * scale_y
        x2 = (boxes[:, 0] + boxes[:, 2] / 2) * scale_x
        y2 = (boxes[:, 1] + boxes[:, 3] / 2) * scale_y
        
        # 应用NMS
        for i in range(len(self.class_names)):
            class_scores = scores[:, i]
            valid_indices = class_scores > self.confidence_threshold
            
            if not np.any(valid_indices):
                continue
            
            class_boxes = np.column_stack([x1[valid_indices], y1[valid_indices], 
                                         x2[valid_indices], y2[valid_indices]])
            class_scores_valid = class_scores[valid_indices]
            
            # OpenCV NMS
            indices = cv2.dnn.NMSBoxes(
                class_boxes.tolist(), 
                class_scores_valid.tolist(),
                self.confidence_threshold, 
                self.nms_threshold
            )
            
            if len(indices) > 0:
                for idx in indices.flatten():
                    class_name = self.class_names[i]
                    if class_name in self.target_classes:
                        bbox = class_boxes[idx].astype(int)
                        confidence = float(class_scores_valid[idx])
                        
                        result = DetectionResult(
                            detection_type='object_detection',
                            confidence=confidence,
                            bbox=tuple(bbox),
                            timestamp=time.time(),
                            class_name=class_name,
                            metadata={
                                'class_id': i,
                                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                            }
                        )
                        results.append(result)
        
        return results
    
    def get_detector_type(self) -> str:
        return "object_detection"

class FaceRecognitionDetector(BaseDetector):
    """人脸识别检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.face_database_path = config.get('face_database', 'data/faces/')
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.known_faces = {}
        self.face_encodings = {}
        
    def load_model(self) -> bool:
        """加载人脸识别模型"""
        try:
            if self.model_path:
                providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                self.session = ort.InferenceSession(self.model_path, providers=providers)
                logger.info(f"人脸识别模型加载成功: {self.model_path}")
            
            # 加载已知人脸数据库
            self._load_face_database()
            return True
            
        except Exception as e:
            logger.error(f"人脸识别模型加载失败: {e}")
            return False
    
    def _load_face_database(self):
        """加载人脸数据库"""
        import os
        if os.path.exists(self.face_database_path):
            # 这里应该加载预先计算的人脸特征
            # 简化实现，实际应该使用深度学习人脸识别模型
            logger.info(f"人脸数据库路径: {self.face_database_path}")
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行人脸检测和识别"""
        if not self.enabled:
            return []
        
        try:
            results = []
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测人脸
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            
            for (x, y, w, h) in faces:
                confidence = 0.8  # 简化实现，实际应该从模型获取
                
                if confidence > self.confidence_threshold:
                    result = DetectionResult(
                        detection_type='face_recognition',
                        confidence=confidence,
                        bbox=(x, y, x + w, y + h),
                        timestamp=time.time(),
                        metadata={
                            'face_size': w * h,
                            'identity': 'unknown'  # 实际应该进行人脸识别
                        }
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"人脸检测失败: {e}")
            return []
    
    def get_detector_type(self) -> str:
        return "face_recognition"

class LineCrossingDetector(BaseDetector):
    """越线检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.lines = config.get('lines', [])
        self.track_history = {}  # 跟踪历史
        self.crossing_buffer = 5  # 越线缓冲帧数
        
    def load_model(self) -> bool:
        """加载模型（越线检测不需要额外模型）"""
        logger.info("越线检测器初始化完成")
        return True
    
    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行越线检测"""
        if not self.enabled or not self.lines:
            return []
        
        # 需要配合目标检测结果
        object_detections = kwargs.get('object_detections', [])
        if not object_detections:
            return []
        
        results = []
        
        for detection in object_detections:
            if detection.bbox and detection.class_name == 'person':
                # 计算目标中心点
                x1, y1, x2, y2 = detection.bbox
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                
                # 检查是否越线
                for line_config in self.lines:
                    line_name = line_config.get('name', 'unknown')
                    start_point = line_config.get('start', [0, 0])
                    end_point = line_config.get('end', [100, 100])
                    
                    if self._check_line_crossing(center_x, center_y, start_point, end_point, detection.track_id):
                        result = DetectionResult(
                            detection_type='line_crossing',
                            confidence=0.9,
                            bbox=detection.bbox,
                            timestamp=time.time(),
                            metadata={
                                'line_name': line_name,
                                'crossing_point': (center_x, center_y),
                                'line_start': start_point,
                                'line_end': end_point,
                                'object_class': detection.class_name
                            }
                        )
                        results.append(result)
        
        return results
    
    def _check_line_crossing(self, x: int, y: int, line_start: List[int], line_end: List[int], track_id: Optional[int]) -> bool:
        """检查是否越线"""
        # 简化的越线检测逻辑
        # 实际应该实现更复杂的轨迹分析
        if track_id is None:
            return False
        
        # 计算点到线的距离
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 使用点到直线距离公式
        distance = abs((y2 - y1) * x - (x2 - x1) * y + x2 * y1 - y2 * x1) / np.sqrt((y2 - y1)**2 + (x2 - x1)**2)
        
        # 如果距离小于阈值，认为接近线
        threshold = 20
        return distance < threshold
    
    def get_detector_type(self) -> str:
        return "line_crossing"

class CameraObstructionDetector(BaseDetector):
    """摄像头遮挡/异常检测器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.sensitivity = config.get('sensitivity', 0.8)
        self.check_interval = config.get('check_interval', 10)  # 检查间隔（秒）
        self.baseline_frame = None
        self.baseline_histogram = None
        self.last_check_time = 0
        self.frame_buffer = deque(maxlen=30)  # 保存最近30帧用于分析

    def load_model(self) -> bool:
        """加载模型（遮挡检测不需要额外模型）"""
        logger.info("摄像头遮挡检测器初始化完成")
        return True

    def detect(self, frame: np.ndarray, **kwargs) -> List[DetectionResult]:
        """执行摄像头遮挡检测"""
        if not self.enabled:
            return []

        current_time = time.time()

        # 按间隔检查
        if current_time - self.last_check_time < self.check_interval:
            return []

        self.last_check_time = current_time
        self.frame_buffer.append(frame.copy())

        # 需要足够的帧数进行分析
        if len(self.frame_buffer) < 10:
            return []

        results = []

        # 检查是否遮挡
        if self._detect_obstruction(frame):
            result = DetectionResult(
                detection_type='camera_obstruction',
                confidence=0.9,
                bbox=None,
                timestamp=current_time,
                metadata={
                    'obstruction_type': 'blocked',
                    'severity': 'high'
                }
            )
            results.append(result)

        # 检查是否异常（如画面冻结、过暗等）
        anomaly_type = self._detect_anomaly(frame)
        if anomaly_type:
            result = DetectionResult(
                detection_type='camera_obstruction',
                confidence=0.8,
                bbox=None,
                timestamp=current_time,
                metadata={
                    'obstruction_type': anomaly_type,
                    'severity': 'medium'
                }
            )
            results.append(result)

        return results

    def _detect_obstruction(self, frame: np.ndarray) -> bool:
        """检测摄像头是否被遮挡"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 计算图像的标准差（衡量图像复杂度）
            std_dev = np.std(gray)

            # 如果标准差过低，可能是被遮挡
            if std_dev < 10:  # 阈值可配置
                return True

            # 检查是否大部分区域都是相同颜色
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist_normalized = hist / hist.sum()

            # 如果某个颜色占比过高，可能是遮挡
            max_bin_ratio = np.max(hist_normalized)
            if max_bin_ratio > self.sensitivity:
                return True

            return False

        except Exception as e:
            logger.error(f"遮挡检测失败: {e}")
            return False

    def _detect_anomaly(self, frame: np.ndarray) -> Optional[str]:
        """检测摄像头异常"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 检查是否过暗
            mean_brightness = np.mean(gray)
            if mean_brightness < 20:
                return 'too_dark'

            # 检查是否过亮
            if mean_brightness > 240:
                return 'too_bright'

            # 检查是否画面冻结（与前几帧对比）
            if len(self.frame_buffer) >= 5:
                prev_frame = self.frame_buffer[-5]
                prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

                # 计算帧差
                diff = cv2.absdiff(gray, prev_gray)
                diff_mean = np.mean(diff)

                # 如果连续几帧几乎没有变化，可能是画面冻结
                if diff_mean < 2:
                    return 'frozen'

            return None

        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            return None

    def get_detector_type(self) -> str:
        return "camera_obstruction"

class DetectionEngine:
    """检测引擎主类 - 管理所有检测器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.detectors: Dict[str, BaseDetector] = {}
        self.detection_stats = {
            'total_detections': 0,
            'detections_by_type': {},
            'last_detection_time': 0,
            'fps': 0,
            'frame_count': 0,
            'start_time': time.time()
        }
        self.performance_monitor = {
            'inference_times': deque(maxlen=100),
            'memory_usage': deque(maxlen=100),
            'gpu_usage': deque(maxlen=100)
        }

        # 初始化检测器
        self._initialize_detectors()

    def _initialize_detectors(self):
        """初始化所有检测器"""
        detector_configs = self.config.get('detectors', {})

        # 注册检测器类
        detector_classes = {
            'object_detection': ObjectDetector,
            'face_recognition': FaceRecognitionDetector,
            'line_crossing': LineCrossingDetector,
            'camera_obstruction': CameraObstructionDetector,
            'abandoned_object': None,  # 将在下面动态导入
            'overflow_detection': None,
            'line_crossing_counter': None,
            'camera_tampering': None
        }
        
        # 动态导入新的检测器
        try:
            from .abandoned_object_detector import AbandonedObjectDetector
            detector_classes['abandoned_object'] = AbandonedObjectDetector
        except ImportError:
            logger.warning("AbandonedObjectDetector 导入失败")
        
        try:
            from .overflow_detector import OverflowDetector
            detector_classes['overflow_detection'] = OverflowDetector
        except ImportError:
            logger.warning("OverflowDetector 导入失败")
        
        try:
            from .line_crossing_counter import LineCrossingCounter
            detector_classes['line_crossing_counter'] = LineCrossingCounter
        except ImportError:
            logger.warning("LineCrossingCounter 导入失败")
        
        try:
            from .camera_tampering_detector import CameraTamperingDetector
            detector_classes['camera_tampering'] = CameraTamperingDetector
        except ImportError:
            logger.warning("CameraTamperingDetector 导入失败")
        
        try:
            from .face_recognition_detector import FaceRecognitionDetector as EnhancedFaceRecognitionDetector
            detector_classes['enhanced_face_recognition'] = EnhancedFaceRecognitionDetector
        except ImportError:
            logger.warning("EnhancedFaceRecognitionDetector 导入失败")

        for detector_name, detector_config in detector_configs.items():
            if detector_name in detector_classes and detector_config.get('enabled', False):
                detector_class = detector_classes[detector_name]
                if detector_class is None:
                    logger.warning(f"检测器类 {detector_name} 未能正确导入")
                    continue
                    
                try:
                    detector = detector_class(detector_config)

                    if detector.load_model():
                        self.detectors[detector_name] = detector
                        logger.info(f"检测器 {detector_name} 初始化成功")
                    else:
                        logger.error(f"检测器 {detector_name} 模型加载失败")

                except Exception as e:
                    logger.error(f"检测器 {detector_name} 初始化失败: {e}")

        logger.info(f"检测引擎初始化完成，已加载 {len(self.detectors)} 个检测器")

    def detect(self, camera_id: str, frame: np.ndarray) -> List[DetectionResult]:
        """执行检测"""
        if frame is None or frame.size == 0:
            return []

        start_time = time.time()
        all_results = []

        try:
            # 更新统计信息
            self.detection_stats['frame_count'] += 1

            # 首先执行目标检测（其他检测器可能需要其结果）
            object_results = []
            if 'object_detection' in self.detectors:
                object_results = self.detectors['object_detection'].detect(frame)
                all_results.extend(object_results)

            # 执行其他检测器
            for detector_name, detector in self.detectors.items():
                if detector_name == 'object_detection':
                    continue  # 已经执行过

                try:
                    # 传递目标检测结果给其他检测器
                    kwargs = {'object_detections': object_results}
                    results = detector.detect(frame, **kwargs)
                    all_results.extend(results)

                except Exception as e:
                    logger.error(f"检测器 {detector_name} 执行失败: {e}")

            # 更新统计信息
            inference_time = time.time() - start_time
            self.performance_monitor['inference_times'].append(inference_time)

            self.detection_stats['total_detections'] += len(all_results)
            self.detection_stats['last_detection_time'] = time.time()

            # 按类型统计
            for result in all_results:
                detection_type = result.detection_type
                if detection_type not in self.detection_stats['detections_by_type']:
                    self.detection_stats['detections_by_type'][detection_type] = 0
                self.detection_stats['detections_by_type'][detection_type] += 1

            # 计算FPS
            elapsed_time = time.time() - self.detection_stats['start_time']
            if elapsed_time > 0:
                self.detection_stats['fps'] = self.detection_stats['frame_count'] / elapsed_time

            logger.debug(f"检测完成，耗时: {inference_time:.3f}s，检测到 {len(all_results)} 个目标")

            return all_results

        except Exception as e:
            logger.error(f"检测引擎执行失败: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        avg_inference_time = 0
        if self.performance_monitor['inference_times']:
            avg_inference_time = np.mean(self.performance_monitor['inference_times'])

        return {
            'total_detections': self.detection_stats['total_detections'],
            'detections_by_type': self.detection_stats['detections_by_type'].copy(),
            'fps': round(self.detection_stats['fps'], 2),
            'avg_inference_time': round(avg_inference_time, 3),
            'active_detectors': list(self.detectors.keys()),
            'frame_count': self.detection_stats['frame_count']
        }

    def update_detector_config(self, detector_name: str, config: Dict[str, Any]):
        """更新检测器配置"""
        if detector_name in self.detectors:
            self.detectors[detector_name].config.update(config)
            logger.info(f"检测器 {detector_name} 配置已更新")

    def enable_detector(self, detector_name: str, enabled: bool):
        """启用/禁用检测器"""
        if detector_name in self.detectors:
            self.detectors[detector_name].enabled = enabled
            logger.info(f"检测器 {detector_name} {'启用' if enabled else '禁用'}")

    def load_models(self):
        """加载所有模型"""
        for detector_name, detector in self.detectors.items():
            try:
                detector.load_model()
                logger.info(f"检测器 {detector_name} 模型重新加载成功")
            except Exception as e:
                logger.error(f"检测器 {detector_name} 模型重新加载失败: {e}")

    def cleanup(self):
        """清理资源"""
        for detector in self.detectors.values():
            if hasattr(detector, 'session') and detector.session:
                del detector.session

        self.detectors.clear()
        logger.info("检测引擎资源清理完成")
