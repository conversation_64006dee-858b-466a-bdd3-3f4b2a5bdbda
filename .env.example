# Cerberus 环境配置示例文件
# 复制此文件为 .env 并根据实际环境修改配置

# 环境类型 (development, production, testing)
ENVIRONMENT=development

# 中央平台配置
HOST=0.0.0.0
PORT=8000
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# 数据库配置
DATABASE_URL=postgresql://cerberus:cerberus123@localhost:5432/cerberus
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# MQTT配置
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_KEEPALIVE=60

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=cerberus
MINIO_SECRET_KEY=cerberus123
MINIO_BUCKET=cerberus-videos
MINIO_SECURE=false

# 边缘节点配置
NODE_ID=edge-node-1
CENTRAL_PLATFORM_URL=http://localhost:8000

# 视频处理配置
VIDEO_RESOLUTION=1280x720
VIDEO_FPS=25
VIDEO_CODEC=h264
PRE_RECORD_SECONDS=15
POST_RECORD_SECONDS=15

# AI检测配置
AI_MODEL_PATH=models/
DETECTION_CONFIDENCE_THRESHOLD=0.5
DETECTION_INTERVAL=1.0

# 缓存配置
CACHE_DIR=cache/
CACHE_MAX_SIZE_GB=10
CACHE_CLEANUP_INTERVAL=3600

# 状态报告配置
STATUS_REPORT_INTERVAL=30
DATA_SYNC_INTERVAL=60

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/cerberus.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 数据保留策略
EVENT_RETENTION_DAYS=90
VIDEO_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7

# 邮件通知配置
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_FROM=

# Webhook通知配置
WEBHOOK_ENABLED=false
WEBHOOK_URL=
WEBHOOK_SECRET=

# 性能配置
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=30

# 前端配置
FRONTEND_URL=http://localhost:3000
ALLOWED_HOSTS=localhost,127.0.0.1,cerberus.local
