"""
Cerberus MQTT发布器
负责向中央平台发布事件和状态信息
"""

import json
import time
import logging
import threading
from typing import Dict, Any, Optional, List
import paho.mqtt.client as mqtt
from queue import Queue, Empty

logger = logging.getLogger(__name__)

class MQTTPublisher:
    """MQTT发布器"""
    
    def __init__(self, config):
        self.config = config
        self.client = None
        self.connected = False
        self.reconnect_interval = 5
        self.max_reconnect_attempts = 10
        self.reconnect_attempts = 0
        
        # 消息队列（用于离线时缓存消息）
        self.message_queue = Queue(maxsize=1000)
        self.queue_thread = None
        self.running = False
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_failed': 0,
            'messages_queued': 0,
            'connection_count': 0,
            'last_message_time': 0
        }
        
        # 初始化MQTT客户端
        self._initialize_client()
        
        logger.info("MQTT发布器初始化完成")
    
    def _initialize_client(self):
        """初始化MQTT客户端"""
        try:
            client_id = f"cerberus_edge_{self.config.NODE_ID}_{int(time.time())}"
            self.client = mqtt.Client(client_id=client_id, protocol=mqtt.MQTTv311)
            
            # 设置回调函数
            self.client.on_connect = self._on_connect
            self.client.on_disconnect = self._on_disconnect
            self.client.on_publish = self._on_publish
            self.client.on_log = self._on_log
            
            # 设置认证（如果需要）
            if self.config.MQTT_USERNAME and self.config.MQTT_PASSWORD:
                self.client.username_pw_set(
                    self.config.MQTT_USERNAME,
                    self.config.MQTT_PASSWORD
                )
            
            # 设置遗嘱消息
            will_topic = f"cerberus/nodes/{self.config.NODE_ID}/status"
            will_payload = json.dumps({
                'node_id': self.config.NODE_ID,
                'status': 'offline',
                'timestamp': time.time()
            })
            self.client.will_set(will_topic, will_payload, qos=1, retain=True)
            
            logger.info(f"MQTT客户端初始化完成: {client_id}")
            
        except Exception as e:
            logger.error(f"MQTT客户端初始化失败: {e}")
            raise
    
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            self.connected = True
            self.reconnect_attempts = 0
            self.stats['connection_count'] += 1
            logger.info(f"MQTT连接成功: {self.config.MQTT_BROKER_HOST}:{self.config.MQTT_BROKER_PORT}")
            
            # 发布上线状态
            self._publish_online_status()
            
        else:
            self.connected = False
            logger.error(f"MQTT连接失败，错误码: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        self.connected = False
        if rc != 0:
            logger.warning(f"MQTT意外断开连接，错误码: {rc}")
            self._schedule_reconnect()
        else:
            logger.info("MQTT正常断开连接")
    
    def _on_publish(self, client, userdata, mid):
        """发布回调"""
        self.stats['messages_sent'] += 1
        self.stats['last_message_time'] = time.time()
        logger.debug(f"消息发布成功，消息ID: {mid}")
    
    def _on_log(self, client, userdata, level, buf):
        """日志回调"""
        if level == mqtt.MQTT_LOG_ERR:
            logger.error(f"MQTT错误: {buf}")
        elif level == mqtt.MQTT_LOG_WARNING:
            logger.warning(f"MQTT警告: {buf}")
        else:
            logger.debug(f"MQTT日志: {buf}")
    
    def connect(self) -> bool:
        """连接到MQTT代理"""
        try:
            if self.connected:
                return True
            
            logger.info(f"连接MQTT代理: {self.config.MQTT_BROKER_HOST}:{self.config.MQTT_BROKER_PORT}")
            
            self.client.connect(
                self.config.MQTT_BROKER_HOST,
                self.config.MQTT_BROKER_PORT,
                self.config.MQTT_KEEPALIVE
            )
            
            # 启动网络循环
            self.client.loop_start()
            
            # 启动消息队列处理线程
            self._start_queue_processor()
            
            return True
            
        except Exception as e:
            logger.error(f"MQTT连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开MQTT连接"""
        try:
            self.running = False
            
            if self.connected:
                # 发布下线状态
                self._publish_offline_status()
                time.sleep(0.5)  # 等待消息发送
            
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()
            
            # 停止队列处理线程
            if self.queue_thread and self.queue_thread.is_alive():
                self.queue_thread.join(timeout=5)
            
            logger.info("MQTT连接已断开")
            
        except Exception as e:
            logger.error(f"MQTT断开连接失败: {e}")
    
    def _schedule_reconnect(self):
        """安排重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("MQTT重连次数超过限制，停止重连")
            return
        
        self.reconnect_attempts += 1
        wait_time = min(self.reconnect_interval * self.reconnect_attempts, 60)
        
        logger.info(f"安排MQTT重连，等待 {wait_time} 秒（第 {self.reconnect_attempts} 次尝试）")
        
        def reconnect():
            time.sleep(wait_time)
            if not self.connected:
                self.connect()
        
        reconnect_thread = threading.Thread(target=reconnect, daemon=True)
        reconnect_thread.start()
    
    def _start_queue_processor(self):
        """启动消息队列处理器"""
        if self.queue_thread and self.queue_thread.is_alive():
            return
        
        self.running = True
        self.queue_thread = threading.Thread(target=self._process_message_queue, daemon=True)
        self.queue_thread.start()
        logger.info("消息队列处理器已启动")
    
    def _process_message_queue(self):
        """处理消息队列"""
        while self.running:
            try:
                # 从队列获取消息
                message = self.message_queue.get(timeout=1.0)
                
                if self.connected:
                    # 发送消息
                    result = self.client.publish(
                        message['topic'],
                        message['payload'],
                        qos=message['qos'],
                        retain=message['retain']
                    )
                    
                    if result.rc == mqtt.MQTT_ERR_SUCCESS:
                        logger.debug(f"队列消息发送成功: {message['topic']}")
                    else:
                        logger.error(f"队列消息发送失败: {result.rc}")
                        self.stats['messages_failed'] += 1
                else:
                    # 如果未连接，重新放回队列
                    if not self.message_queue.full():
                        self.message_queue.put(message)
                    else:
                        logger.warning("消息队列已满，丢弃消息")
                        self.stats['messages_failed'] += 1
                
            except Empty:
                continue
            except Exception as e:
                logger.error(f"处理消息队列错误: {e}")
                time.sleep(1)
    
    def _publish_message(self, topic: str, payload: Dict[str, Any], qos: int = 1, retain: bool = False):
        """发布消息（内部方法）"""
        try:
            message_payload = json.dumps(payload, ensure_ascii=False)
            
            if self.connected:
                # 直接发送
                result = self.client.publish(topic, message_payload, qos=qos, retain=retain)
                if result.rc != mqtt.MQTT_ERR_SUCCESS:
                    logger.error(f"消息发送失败: {result.rc}")
                    self.stats['messages_failed'] += 1
            else:
                # 添加到队列
                if not self.message_queue.full():
                    self.message_queue.put({
                        'topic': topic,
                        'payload': message_payload,
                        'qos': qos,
                        'retain': retain
                    })
                    self.stats['messages_queued'] += 1
                    logger.debug(f"消息已加入队列: {topic}")
                else:
                    logger.warning("消息队列已满，丢弃消息")
                    self.stats['messages_failed'] += 1
                    
        except Exception as e:
            logger.error(f"发布消息失败: {e}")
            self.stats['messages_failed'] += 1
    
    def publish_event(self, event_data: Dict[str, Any]):
        """发布检测事件"""
        topic = f"cerberus/events/{self.config.NODE_ID}/{event_data.get('camera_id', 'unknown')}"
        self._publish_message(topic, event_data, qos=1)
        logger.debug(f"发布事件: {event_data.get('event_type', 'unknown')}")
    
    def publish_status(self, status_data: Dict[str, Any]):
        """发布节点状态"""
        topic = f"cerberus/nodes/{self.config.NODE_ID}/status"
        
        # 添加节点ID和时间戳
        status_data.update({
            'node_id': self.config.NODE_ID,
            'timestamp': time.time()
        })
        
        self._publish_message(topic, status_data, qos=1, retain=True)
        logger.debug("发布节点状态")
    
    def publish_heartbeat(self, heartbeat_data: Dict[str, Any]):
        """发布心跳"""
        topic = f"cerberus/nodes/{self.config.NODE_ID}/heartbeat"
        
        # 添加节点ID和时间戳
        heartbeat_data.update({
            'node_id': self.config.NODE_ID,
            'timestamp': time.time()
        })
        
        self._publish_message(topic, heartbeat_data, qos=0)
        logger.debug("发布心跳")
    
    def _publish_online_status(self):
        """发布上线状态"""
        status_data = {
            'node_id': self.config.NODE_ID,
            'status': 'online',
            'timestamp': time.time(),
            'version': '2.0.0'
        }
        
        topic = f"cerberus/nodes/{self.config.NODE_ID}/status"
        self._publish_message(topic, status_data, qos=1, retain=True)
    
    def _publish_offline_status(self):
        """发布下线状态"""
        status_data = {
            'node_id': self.config.NODE_ID,
            'status': 'offline',
            'timestamp': time.time()
        }
        
        topic = f"cerberus/nodes/{self.config.NODE_ID}/status"
        self._publish_message(topic, status_data, qos=1, retain=True)
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'connected': self.connected,
            'messages_sent': self.stats['messages_sent'],
            'messages_failed': self.stats['messages_failed'],
            'messages_queued': self.stats['messages_queued'],
            'queue_size': self.message_queue.qsize(),
            'connection_count': self.stats['connection_count'],
            'last_message_time': self.stats['last_message_time'],
            'reconnect_attempts': self.reconnect_attempts
        }
