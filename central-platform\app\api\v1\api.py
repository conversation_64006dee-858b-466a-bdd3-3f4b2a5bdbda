"""
API路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth, users, nodes, cameras, events, analytics, settings, system
)

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户管理路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 节点管理路由
api_router.include_router(nodes.router, prefix="/nodes", tags=["节点管理"])

# 摄像头管理路由
api_router.include_router(cameras.router, prefix="/cameras", tags=["摄像头管理"])

# 事件管理路由
api_router.include_router(events.router, prefix="/events", tags=["事件管理"])

# 统计分析路由
api_router.include_router(analytics.router, prefix="/analytics", tags=["统计分析"])

# 系统设置路由
api_router.include_router(settings.router, prefix="/settings", tags=["系统设置"])

# 系统信息路由
api_router.include_router(system.router, prefix="/system", tags=["系统信息"])
