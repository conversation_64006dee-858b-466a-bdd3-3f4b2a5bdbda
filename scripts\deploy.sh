#!/bin/bash

# Cerberus 自动部署脚本
# 用法: ./scripts/deploy.sh [dev|prod]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装，请先安装 Git"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        sudo mkdir -p /opt/cerberus/{data,logs,config,backups,models}
        sudo mkdir -p /opt/cerberus/data/{postgres,redis,mqtt,minio,edge-cache-1,prometheus,grafana}
        sudo mkdir -p /opt/cerberus/logs/{mqtt,nginx,fluentd}
        sudo chown -R $USER:$USER /opt/cerberus
    else
        mkdir -p logs cache models config
    fi
    
    log_success "目录创建完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        log_warning "已创建 .env 文件，请根据需要修改配置"
    fi
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        # 生产环境配置
        if [ ! -f .env.prod ]; then
            cp .env.example .env.prod
            
            # 生成安全密钥
            SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
            sed -i "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" .env.prod
            
            log_warning "已创建 .env.prod 文件，请修改生产环境配置"
        fi
    fi
    
    log_success "环境配置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml build
    else
        docker-compose build
    fi
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        # 生产环境启动
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    else
        # 开发环境启动
        docker-compose up -d
    fi
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待数据库启动..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U cerberus &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            log_error "数据库启动超时"
            exit 1
        fi
    done
    
    # 等待Redis
    log_info "等待Redis启动..."
    timeout=30
    while ! docker-compose exec -T redis redis-cli ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            log_error "Redis启动超时"
            exit 1
        fi
    done
    
    # 等待中央平台
    log_info "等待中央平台启动..."
    timeout=60
    while ! curl -s http://localhost:8000/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            log_error "中央平台启动超时"
            exit 1
        fi
    done
    
    log_success "所有服务已就绪"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待中央平台完全启动
    sleep 10
    
    # 运行迁移
    docker-compose exec central-platform alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查中央平台
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        log_success "中央平台: 健康"
    else
        log_error "中央平台: 异常"
        return 1
    fi
    
    # 检查边缘节点
    if curl -s http://localhost:5000/health | grep -q "healthy"; then
        log_success "边缘节点: 健康"
    else
        log_warning "边缘节点: 异常 (可能正在启动)"
    fi
    
    # 检查数据库
    if docker-compose exec -T postgres pg_isready -U cerberus &>/dev/null; then
        log_success "PostgreSQL: 健康"
    else
        log_error "PostgreSQL: 异常"
        return 1
    fi
    
    # 检查Redis
    if docker-compose exec -T redis redis-cli ping &>/dev/null; then
        log_success "Redis: 健康"
    else
        log_error "Redis: 异常"
        return 1
    fi
    
    log_success "健康检查完成"
}

# 显示访问信息
show_access_info() {
    log_success "🎉 Cerberus 部署完成！"
    echo
    echo "访问地址:"
    echo "  Web管理界面: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
    echo "  MQTT管理: http://localhost:18083 (admin/public)"
    echo "  MinIO管理: http://localhost:9001 (cerberus/cerberus123)"
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        echo "  Prometheus: http://localhost:9090"
        echo "  Grafana: http://localhost:3000 (admin/admin)"
    fi
    
    echo
    echo "常用命令:"
    echo "  查看服务状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo
}

# 主函数
main() {
    # 获取环境参数
    ENVIRONMENT=${1:-dev}
    
    if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "prod" ]; then
        log_error "无效的环境参数，请使用 'dev' 或 'prod'"
        echo "用法: $0 [dev|prod]"
        exit 1
    fi
    
    log_info "开始部署 Cerberus ($ENVIRONMENT 环境)..."
    
    # 执行部署步骤
    check_dependencies
    create_directories
    setup_environment
    build_images
    start_services
    wait_for_services
    run_migrations
    health_check
    show_access_info
    
    log_success "部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
