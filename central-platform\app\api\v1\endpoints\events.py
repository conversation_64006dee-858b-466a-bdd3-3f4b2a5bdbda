"""
事件管理端点
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.event_service import EventService

router = APIRouter()

@router.get("/")
async def list_events(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    event_type: Optional[str] = None,
    status: Optional[str] = None,
    severity: Optional[str] = None,
    node_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取事件列表"""
    try:
        event_service = EventService()
        events = await event_service.get_events_by_filters(
            skip=skip,
            limit=limit,
            event_type=event_type,
            status=status,
            severity=severity,
            node_id=node_id
        )
        return events
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching events: {str(e)}")

@router.get("/{event_id}")
async def get_event(event_id: str):
    """获取特定事件信息"""
    try:
        event_service = EventService()
        event = await event_service.get_event_by_id(event_id)
        
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        
        return event
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching event: {str(e)}")

@router.get("/stats/summary")
async def get_events_summary():
    """获取事件统计摘要"""
    try:
        event_service = EventService()
        stats = await event_service.get_event_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching events summary: {str(e)}")
