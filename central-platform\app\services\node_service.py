"""
节点服务类
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.core.database import get_db
from app.models.database import Node, Camera
from app.core.redis_client import cache

logger = logging.getLogger(__name__)

class NodeService:
    """节点管理服务"""
    
    def __init__(self):
        self.cache_ttl = 300  # 5分钟缓存
    
    async def get_total_count(self) -> int:
        """获取节点总数"""
        try:
            cache_key = "nodes:total_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                result = await db.execute(select(func.count(Node.id)))
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting total node count: {e}")
            return 0
    
    async def get_online_count(self) -> int:
        """获取在线节点数"""
        try:
            cache_key = "nodes:online_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Node.id)).where(Node.status == "online")
                )
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting online node count: {e}")
            return 0
    
    async def get_offline_count(self) -> int:
        """获取离线节点数"""
        try:
            total = await self.get_total_count()
            online = await self.get_online_count()
            return total - online
        except Exception as e:
            logger.error(f"Error getting offline node count: {e}")
            return 0
    
    async def get_total_cameras(self) -> int:
        """获取摄像头总数"""
        try:
            cache_key = "cameras:total_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                result = await db.execute(select(func.count(Camera.id)))
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting total camera count: {e}")
            return 0
    
    async def get_active_cameras(self) -> int:
        """获取活跃摄像头数"""
        try:
            cache_key = "cameras:active_count"
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            async for db in get_db():
                result = await db.execute(
                    select(func.count(Camera.id)).where(Camera.status == "active")
                )
                count = result.scalar()
                await cache.set(cache_key, count, self.cache_ttl)
                return count
        except Exception as e:
            logger.error(f"Error getting active camera count: {e}")
            return 0
    
    async def get_node_by_id(self, node_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取节点信息"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(Node).where(Node.node_id == node_id)
                )
                node = result.scalar_one_or_none()
                
                if node:
                    return {
                        "id": node.id,
                        "node_id": node.node_id,
                        "name": node.name,
                        "description": node.description,
                        "location": node.location,
                        "ip_address": node.ip_address,
                        "status": node.status,
                        "last_heartbeat": node.last_heartbeat,
                        "version": node.version,
                        "config": node.config,
                        "metadata": node.node_metadata,
                        "created_at": node.created_at,
                        "updated_at": node.updated_at
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting node {node_id}: {e}")
            return None
    
    async def update_node_status(self, node_id: str, status: str, heartbeat_time: datetime = None) -> bool:
        """更新节点状态"""
        try:
            if heartbeat_time is None:
                heartbeat_time = datetime.utcnow()
            
            async for db in get_db():
                result = await db.execute(
                    select(Node).where(Node.node_id == node_id)
                )
                node = result.scalar_one_or_none()
                
                if node:
                    node.status = status
                    node.last_heartbeat = heartbeat_time
                    await db.commit()
                    
                    # 清除相关缓存
                    await cache.delete("nodes:online_count")
                    await cache.delete("nodes:total_count")
                    
                    return True
                return False
        except Exception as e:
            logger.error(f"Error updating node status for {node_id}: {e}")
            return False
    
    async def register_node(self, node_data: Dict[str, Any]) -> bool:
        """注册新节点"""
        try:
            async for db in get_db():
                # 检查节点是否已存在
                result = await db.execute(
                    select(Node).where(Node.node_id == node_data["node_id"])
                )
                existing_node = result.scalar_one_or_none()
                
                if existing_node:
                    # 更新现有节点信息
                    for key, value in node_data.items():
                        if hasattr(existing_node, key):
                            setattr(existing_node, key, value)
                else:
                    # 创建新节点
                    new_node = Node(**node_data)
                    db.add(new_node)
                
                await db.commit()
                
                # 清除相关缓存
                await cache.clear_pattern("nodes:*")
                
                return True
        except Exception as e:
            logger.error(f"Error registering node: {e}")
            return False
    
    async def get_nodes_list(self, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取节点列表"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(Node).offset(skip).limit(limit).order_by(Node.created_at.desc())
                )
                nodes = result.scalars().all()
                
                return [
                    {
                        "id": node.id,
                        "node_id": node.node_id,
                        "name": node.name,
                        "status": node.status,
                        "location": node.location,
                        "ip_address": node.ip_address,
                        "last_heartbeat": node.last_heartbeat,
                        "created_at": node.created_at,
                        "updated_at": node.updated_at
                    }
                    for node in nodes
                ]
        except Exception as e:
            logger.error(f"Error getting nodes list: {e}")
            return []
    
    async def check_node_health(self) -> Dict[str, Any]:
        """检查所有节点健康状态"""
        try:
            current_time = datetime.utcnow()
            offline_threshold = current_time - timedelta(minutes=5)  # 5分钟无心跳视为离线
            
            async for db in get_db():
                # 更新离线节点状态
                await db.execute(
                    Node.__table__.update()
                    .where(
                        and_(
                            Node.last_heartbeat < offline_threshold,
                            Node.status != "offline"
                        )
                    )
                    .values(status="offline")
                )
                await db.commit()
                
                # 获取统计信息
                total_result = await db.execute(select(func.count(Node.id)))
                total = total_result.scalar()
                
                online_result = await db.execute(
                    select(func.count(Node.id)).where(Node.status == "online")
                )
                online = online_result.scalar()
                
                offline = total - online
                
                # 清除缓存
                await cache.clear_pattern("nodes:*")
                
                return {
                    "total": total,
                    "online": online,
                    "offline": offline,
                    "health_check_time": current_time.isoformat()
                }
        except Exception as e:
            logger.error(f"Error checking node health: {e}")
            return {"total": 0, "online": 0, "offline": 0}
