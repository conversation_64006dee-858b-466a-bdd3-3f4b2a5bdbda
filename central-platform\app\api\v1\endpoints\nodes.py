"""
节点管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.node_service import NodeService

router = APIRouter()

@router.get("/", response_model=NodeListResponse)
async def list_nodes(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    status: Optional[str] = Query(None, description="节点状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取节点列表"""
    try:
        node_service = NodeService(db)
        
        filters = {}
        if status:
            filters["status"] = status
        if search:
            filters["search"] = search
        
        nodes, total = await node_service.get_nodes(
            skip=skip,
            limit=limit,
            filters=filters
        )
        
        return NodeListResponse(
            items=nodes,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"获取节点列表失败: {str(e)}"
        )

@router.get("/{node_id}", response_model=NodeResponse)
async def get_node(
    node_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定节点详情"""
    try:
        node_service = NodeService(db)
        node = await node_service.get_node_by_id(node_id)
        
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        return node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"获取节点详情失败: {str(e)}"
        )

@router.post("/", response_model=NodeResponse, status_code=StatusCodes.CREATED)
async def create_node(
    node_data: NodeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新节点"""
    try:
        node_service = NodeService(db)
        
        # 检查节点ID是否已存在
        existing_node = await node_service.get_node_by_id(node_data.node_id)
        if existing_node:
            raise HTTPException(
                status_code=StatusCodes.CONFLICT,
                detail="节点ID已存在"
            )
        
        node = await node_service.create_node(node_data)
        return node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"创建节点失败: {str(e)}"
        )

@router.put("/{node_id}", response_model=NodeResponse)
async def update_node(
    node_id: str,
    node_data: NodeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新节点信息"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        updated_node = await node_service.update_node(node_id, node_data)
        return updated_node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"更新节点失败: {str(e)}"
        )

@router.delete("/{node_id}", status_code=StatusCodes.NO_CONTENT)
async def delete_node(
    node_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除节点"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        await node_service.delete_node(node_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"删除节点失败: {str(e)}"
        )

@router.post("/{node_id}/status", response_model=NodeResponse)
async def update_node_status(
    node_id: str,
    status_data: NodeStatusUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新节点状态"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        updated_node = await node_service.update_node_status(node_id, status_data)
        return updated_node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"更新节点状态失败: {str(e)}"
        )

@router.post("/{node_id}/config", response_model=dict)
async def update_node_config(
    node_id: str,
    config_data: NodeConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新节点配置"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        # 发送配置更新命令到节点
        result = await node_service.send_config_update(node_id, config_data)
        return {"status": "success", "message": "配置更新命令已发送", "result": result}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"更新节点配置失败: {str(e)}"
        )

@router.get("/{node_id}/cameras")
async def get_node_cameras(
    node_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取节点的摄像头列表"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        cameras = await node_service.get_node_cameras(node_id)
        return {"cameras": cameras}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"获取节点摄像头失败: {str(e)}"
        )

@router.get("/{node_id}/events")
async def get_node_events(
    node_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    event_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取节点的事件列表"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        filters = {"node_id": node_id}
        if event_type:
            filters["event_type"] = event_type
        
        events, total = await node_service.get_node_events(
            node_id=node_id,
            skip=skip,
            limit=limit,
            filters=filters
        )
        
        return {
            "events": events,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"获取节点事件失败: {str(e)}"
        )

@router.post("/{node_id}/restart")
async def restart_node(
    node_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重启节点"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        result = await node_service.restart_node(node_id)
        return {"status": "success", "message": "节点重启命令已发送", "result": result}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"重启节点失败: {str(e)}"
        )

@router.get("/{node_id}/health")
async def get_node_health(
    node_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取节点健康状态"""
    try:
        node_service = NodeService(db)
        
        node = await node_service.get_node_by_id(node_id)
        if not node:
            raise HTTPException(
                status_code=StatusCodes.NOT_FOUND,
                detail="节点不存在"
            )
        
        health_status = await node_service.get_node_health(node_id)
        return health_status
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            detail=f"获取节点健康状态失败: {str(e)}"
        )
