"""
节点管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.node_service import NodeService

router = APIRouter()

@router.get("/")
async def list_nodes(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取节点列表"""
    try:
        node_service = NodeService()
        nodes = await node_service.get_nodes_list(skip=skip, limit=limit)
        return nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes: {str(e)}")

@router.get("/{node_id}")
async def get_node(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取特定节点信息"""
    try:
        node_service = NodeService()
        node = await node_service.get_node_by_id(node_id)
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return node
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node: {str(e)}")

@router.get("/{node_id}/status")
async def get_node_status(node_id: str, db: AsyncSession = Depends(get_db)):
    """获取节点状态"""
    try:
        node_service = NodeService()
        node = await node_service.get_node_by_id(node_id)
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        return {
            "node_id": node_id,
            "status": node["status"],
            "last_heartbeat": node["last_heartbeat"].isoformat() if node["last_heartbeat"] else None
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching node status: {str(e)}")

@router.get("/stats/summary")
async def get_nodes_summary(db: AsyncSession = Depends(get_db)):
    """获取节点统计摘要"""
    try:
        node_service = NodeService()
        health_check = await node_service.check_node_health()
        return health_check
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching nodes summary: {str(e)}")
