"""
Redis客户端管理
"""

import logging
import json
from typing import Any, Optional
import redis.asyncio as redis

from app.core.config import get_settings

logger = logging.getLogger(__name__)

# 全局Redis客户端
_redis_client: Optional[redis.Redis] = None

async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client
    if _redis_client is None:
        settings = get_settings()
        _redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True
        )
    return _redis_client

async def close_redis_client():
    """关闭Redis客户端"""
    global _redis_client
    if _redis_client:
        await _redis_client.close()
        _redis_client = None
        logger.info("Redis client closed")

class RedisCache:
    """Redis缓存操作类"""
    
    def __init__(self):
        self.client = None
    
    async def get_client(self):
        """获取Redis客户端"""
        if self.client is None:
            self.client = await get_redis_client()
        return self.client
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存"""
        try:
            client = await self.get_client()
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            if ttl:
                return await client.setex(key, ttl, value)
            else:
                return await client.set(key, value)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    async def get(self, key: str) -> Any:
        """获取缓存"""
        try:
            client = await self.get_client()
            value = await client.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            client = await self.get_client()
            return bool(await client.delete(key))
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            client = await self.get_client()
            return bool(await client.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置键的过期时间"""
        try:
            client = await self.get_client()
            return bool(await client.expire(key, ttl))
        except Exception as e:
            logger.error(f"Error setting expiry for cache key {key}: {e}")
            return False
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配模式的所有键"""
        try:
            client = await self.get_client()
            return await client.keys(pattern)
        except Exception as e:
            logger.error(f"Error getting keys with pattern {pattern}: {e}")
            return []
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的所有键"""
        try:
            keys = await self.keys(pattern)
            if keys:
                client = await self.get_client()
                return await client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Error clearing keys with pattern {pattern}: {e}")
            return 0

# 全局缓存实例
cache = RedisCache()
